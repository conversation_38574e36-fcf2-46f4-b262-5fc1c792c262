# NLWeb Release Notes - June 23 2025

This release introduces significant enhancements to NLWeb's retrieval system, adds powerful tool calling capabilities, and provides new configuration options for response headers.

## Major Features

### 1. Multi-Backend Retrieval System (PR #214)
NLWeb now supports concurrent querying across multiple retrieval backends, providing improved performance and redundancy.

**Key improvements:**
- Multiple backends can be simultaneously active (previously only one at a time)
- Parallel querying across all enabled backends with automatic deduplication
- Configurable write endpoint while reading from multiple sources
- Support for Azure AI Search, Qdrant, Milvus, OpenSearch, and Snowflake backends

[Learn more about the retrieval system →](../nlweb-retrieval.md)

### 2. Tool Calling Framework (PRs #219, #208, #217)
A new extensible tool system enables specialized handling of different query types beyond simple search.

**Available tools:**
- **Search**: Traditional keyword and semantic search (default)
- **Details**: Retrieve specific information about named items (PR #217)
- **Compare**: Side-by-side comparison of two items
- **Ensemble**: Create cohesive sets of related items (PR #219)
- **Recipe Tools**: Ingredient substitutions and accompaniment suggestions (PR #208)

**Example ensemble queries:**
- "Give me an appetizer, main and dessert for an Italian dinner"
- "I'm visiting Seattle for a day - suggest museums and nearby restaurants"
- "What should I wear for hiking in Colorado in winter?"
- "Plan a romantic date night with dinner and entertainment"
- "Create a workout routine with warmup, main exercises, and cooldown"

[Explore the tools system →](../tools.md)

### 3. Configurable Response Headers (PR #205)
NLWeb instances can now define custom headers that are sent as messages at the beginning of each response, enabling:
- License specification (e.g., MIT License with link to terms)
- Data retention policies (e.g., "may be retained for up to 1 day")
- UI component specifications for rendering results
- Custom metadata for your deployment

[Configure response headers →](../nlweb-headers.md)

### 4. Enhanced Debug Panel
The web interface debug panel has been significantly improved:
- Real-time display of tool selection process
- Streaming updates for multi-stage operations
- Clear visualization of backend queries
- Performance metrics for each operation phase

### 5. Comprehensive Testing Framework (PR #167)
New testing infrastructure ensures reliability:
- End-to-end query testing with configurable test suites
- Multi-backend retrieval verification
- Tool selection accuracy tests
- Performance benchmarking utilities
- Database operation testing

## Configuration Changes

### Retrieval Configuration
```yaml
# New format in config_retrieval.yaml
write_endpoint: qdrant_local

endpoints:
  backend_name:
    enabled: true  # Enable/disable without removing config
    db_type: azure_ai_search
    # ... other settings
```

### Tool Selection
- Enable/disable tool selection via `tool_selection_enabled` in config_nlweb.yaml
- Tools defined in XML for easy customization
- Per-type tool inheritance following schema.org hierarchy

## Migration Notes

### From Single to Multi-Backend
1. Update `config_retrieval.yaml` to new format
2. Set `enabled: true` for existing backend
3. Ensure `write_endpoint` points to primary backend
4. Test thoroughly before enabling additional backends

### Tool System Adoption
- Tool selection is enabled by default
- Set `tool_selection_enabled: false` to maintain previous behavior
- Queries with `generate_mode` set to "summarize" or "generate" skip tool selection

## Performance Improvements

- Parallel backend queries reduce latency for multi-source deployments
- Async tool evaluation speeds up request routing
- Optimized deduplication algorithm for large result sets
- Streaming responses for better perceived performance

## Developer Features

- Simplified handler base class for creating custom tools
- Improved logging system with configurable loggers per module
- Better error handling with development/production modes
- Comprehensive type hints and documentation

## Bug Fixes

- Fixed CONFIG UnboundLocalError in decontextualize module (PR #206)
- Resolved ensemble tool ranking errors with tuple handling (PR #219)
- Corrected multi-database connectivity issues (PR #214)
- Fixed FastTrack abort conditions for non-search tools
- Improved decontextualization with URL-based retrieval support (PR #206)

## Coming Soon

- Remote tool support for distributed NLWeb instances
- MCP (Model Context Protocol) server integration
- Dynamic tool loading from external sources
- Enhanced ensemble strategies for complex queries
- GraphQL API endpoint option

## Upgrading

1. Back up your configuration files
2. Update retrieval backend configuration to new format
3. Review and adjust tool selection settings
4. Test with your existing queries
5. Enable new features incrementally

For detailed upgrade instructions, see the documentation for each major feature area.

---

*For questions or issues, please refer to our [GitHub repository](https://github.com/microsoft/NLWeb) or documentation.*
