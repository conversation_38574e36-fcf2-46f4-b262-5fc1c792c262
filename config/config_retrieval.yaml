write_endpoint: qdrant_url

endpoints:

  nlweb_west:
    enabled: true
    api_key_env: NLWEB_WEST_API_KEY
    api_endpoint_env: NLWEB_WEST_ENDPOINT
    index_name: embeddings1536
    db_type: azure_ai_search

  azure_ai_search:
    enabled: false
    api_key_env: AZURE_VECTOR_SEARCH_API_KEY
    api_endpoint_env: AZURE_VECTOR_SEARCH_ENDPOINT
    index_name: embeddings1536
    db_type: azure_ai_search

  azure_ai_search_backup:
    enabled: false
    api_key_env: AZURE_VECTOR_SEARCH_API_KEY_BACKUP
    api_endpoint_env: AZURE_VECTOR_SEARCH_ENDPOINT_BACKUP
    index_name: embeddings1536
    db_type: azure_ai_search
    name: NLWeb_Crawl_Backup
  
  elasticsearch:
    enabled: false
    # Elasticsearch endpoint (localhost or remote URL with Elastic Cloud/Serverless)
    api_endpoint_env: ELASTICSEARCH_URL
    # Authentication credentials
    api_key_env: ELASTICSEARCH_API_KEY
    # Index name to search in
    index_name: nlweb_embeddings
    # Database type
    db_type: elasticsearch
    # Vector properties
    vector_type:
      type: dense_vector

  milvus:
    enabled: false
    api_endpoint_env: MILVUS_ENDPOINT
    api_key_env: MILVUS_TOKEN
    index_name: nlweb_collection
    db_type: milvus
  
  opensearch_knn:
    enabled: false
    # OpenSearch with k-NN plugin enabled (faster, native vector search)
    api_endpoint_env: OPENSEARCH_ENDPOINT
    # Authentication credentials (username:password for basic auth, or API key)
    api_key_env: OPENSEARCH_CREDENTIALS
    # Index name to search in
    index_name: embeddings
    # Database type
    db_type: opensearch
    # Use k-NN plugin for vector search
    use_knn: true

  opensearch_script:
    enabled: false
    # OpenSearch without k-NN plugin (fallback using script_score)
    api_endpoint_env: OPENSEARCH_ENDPOINT
    # Authentication credentials (username:password for basic auth, or API key)
    api_key_env: OPENSEARCH_CREDENTIALS
    # Index name to search in
    index_name: embeddings
    # Database type
    db_type: opensearch
    # Use script_score for vector similarity (slower but works without plugins)
    use_knn: false

  postgres:
    enabled: true
    # Database connection details (i.e. "postgresql://<HOST>:<PORT>/<DATABASE>?user=<USERNAME>&sslmode=require")
    api_endpoint_env: POSTGRES_CONNECTION_STRING
    # Password for authentication 
    api_key_env: POSTGRES_PASSWORD
    # Index name to search in
    index_name: documents
    # Specify the database type
    db_type: postgres

  # Option 1: Local file-based Qdrant storage
  qdrant_local:
    enabled: false
    # Use local file-based storage with a specific path
    database_path: "../data/db"
    # Set the collection name to use
    index_name: nlweb_collection
    # Specify the database type
    db_type: qdrant
    
  # Option 2: Remote Qdrant server
  qdrant_url:
    enabled: true
    # Connect to a Qdrant server at a specific URL
    api_endpoint_env: QDRANT_URL
    # Optional API key for authentication
    api_key_env: QDRANT_API_KEY
    # Set the collection name to use
    index_name: nlweb_collection
    # Specify the database type
    db_type: qdrant

  snowflake_cortex_search_1:
    enabled: false
    api_key_env: SNOWFLAKE_PAT
    api_endpoint_env: SNOWFLAKE_ACCOUNT_URL
    index_name: SNOWFLAKE_CORTEX_SEARCH_SERVICE
    db_type: snowflake_cortex_search
