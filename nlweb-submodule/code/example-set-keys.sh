
# This script sets the environment variables needed for the webapp to run.
# It is recommended to set these variables in your shell profile (e.g., .bashrc, .zshrc) or in a secure environment.
# This script is for demonstration purposes only and should not be used in production.
# Make sure to replace the placeholder values with your actual keys and endpoints.

# For instructions on obtaining the required Azure service keys, 
# see this guide on retrieving Azure credentials: https://learn.microsoft.com/en-us/azure/ai-services/openai/how-to/key 
# or refer to the Azure Search example: https://learn.microsoft.com/en-us/azure/search/search-security-api-keys
# for step-by-step details.


export AZURE_VECTOR_SEARCH_ENDPOINT=" " 
export AZURE_VECTOR_SEARCH_API_KEY="your key"
export AZURE_OPENAI_ENDPOINT=" "
export AZURE_OPENAI_API_KEY="your key"