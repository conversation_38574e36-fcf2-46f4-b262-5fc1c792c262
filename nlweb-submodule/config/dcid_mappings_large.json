{"variables": {"total population": "Count_Person", "male population": "Count_Person_Male", "female population": "Count_Person_Female", "total number of households": "Count_Household", "total housing units": "Count_HousingUnit", "total number of families": "Count_Family", "median age of population": "Median_Age_Person", "median age of male population": "Median_Age_Person_Male", "median age of female population": "Median_Age_Person_Female", "population under 5 years": "Count_Person_Upto5Years", "population aged 5-9": "Count_Person_5To9Years", "population aged 10-14": "Count_Person_10To14Years", "population aged 15-19": "Count_Person_15To19Years", "population aged 20-24": "Count_Person_20To24Years", "population aged 25-29": "Count_Person_25To29Years", "population aged 30-34": "Count_Person_30To34Years", "population aged 35-39": "Count_Person_35To39Years", "population aged 40-44": "Count_Person_40To44Years", "population aged 45-49": "Count_Person_45To49Years", "population aged 50-54": "Count_Person_50To54Years", "population aged 55-59": "Count_Person_55To59Years", "population aged 60-64": "Count_Person_60To64Years", "population aged 65-69": "Count_Person_65To69Years", "population aged 70-74": "Count_Person_70To74Years", "population aged 75-79": "Count_Person_75To79Years", "population aged 80-84": "Count_Person_80To84Years", "population aged 85+": "Count_Person_85<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "white alone population": "Count_Person_<PERSON><PERSON>lone", "black or african american alone": "Count_Person_BlackOrAfricanAmericanAlone", "asian alone population": "Count_Person_AsianAlone", "american indian and alaska native alone": "Count_Person_AmericanIndianAndAlaskaNativeAlone", "native hawaiian and pacific islander alone": "Count_Person_NativeHawaiianAndOtherPacificIslanderAlone", "two or more races": "Count_Person_TwoOrMoreRaces", "hispanic or latino population": "Count_Person_HispanicOrLatino", "non-hispanic or latino population": "Count_Person_NotHispanicOrLatino", "native born population": "Count_Person_<PERSON>Born", "foreign born population": "Count_Person_<PERSON><PERSON><PERSON>", "naturalized citizens": "Count_Person_Naturalized", "non-us citizens": "Count_Person_NotAUSCitizen", "recent immigrants (2010+)": "Count_Person_ForeignBorn_EnteredUS2010OrLater", "percentage native born": "Percent_Person_NativeBorn", "percentage foreign born": "Percent_Person_ForeignBorn", "percentage naturalized citizens": "Percent_Person_Naturalized", "percentage non-citizens": "Percent_Person_NotAUSCitizen", "percentage recent immigrants": "Percent_Person_ForeignBorn_EnteredUS2010OrLater", "immigrants entered 2000-2009": "Count_Person_ForeignBorn_EnteredUS2000To2009", "immigrants entered 1990-1999": "Count_Person_ForeignBorn_EnteredUS1990To1999", "immigrants entered 1980-1989": "Count_Person_ForeignBorn_EnteredUS1980To1989", "immigrants entered before 1980": "Count_Person_ForeignBorn_EnteredUSBefore1980", "percentage entered 2000-2009": "Percent_Person_ForeignBorn_EnteredUS2000To2009", "percentage entered 1990-1999": "Percent_Person_ForeignBorn_EnteredUS1990To1999", "percentage entered 1980-1989": "Percent_Person_ForeignBorn_EnteredUS1980To1989", "percentage entered before 1980": "Percent_Person_ForeignBorn_EnteredUSBefore1980", "immigrants from europe": "Count_Person_Foreign<PERSON>orn_FromEurope", "immigrants from asia": "Count_Person_ForeignBorn_FromAsia", "immigrants from africa": "Count_Person_ForeignBorn_FromAfrica", "immigrants from oceania": "Count_Person_ForeignBorn_FromOceania", "immigrants from latin america": "Count_Person_ForeignBorn_FromLatinAmerica", "immigrants from north america": "Count_Person_Foreign<PERSON>orn_FromNorthAmerica", "percentage from europe": "Percent_Person_Foreign<PERSON>orn_FromEurope", "percentage from asia": "Percent_Person_Foreign<PERSON>orn_FromAsia", "percentage from africa": "Percent_Person_ForeignBorn_FromAfrica", "percentage from oceania": "Percent_Person_ForeignBorn_FromOceania", "percentage from latin america": "Percent_Person_ForeignBorn_FromLatinAmerica", "percentage from north america": "Percent_Person_ForeignBorn_FromNorthAmerica", "immigrants from mexico": "Count_Person_ForeignBorn_FromMexico", "immigrants from china": "Count_Person_Foreign<PERSON><PERSON>_From<PERSON><PERSON>a", "immigrants from india": "Count_Person_ForeignBorn_FromIndia", "immigrants from philippines": "Count_Person_Foreign<PERSON>orn_FromPhilippines", "immigrants from vietnam": "Count_Person_ForeignBorn_FromVietnam", "immigrants from el salvador": "Count_Person_<PERSON><PERSON>orn_FromElSalvador", "immigrants from cuba": "Count_Person_Foreign<PERSON>orn_FromCuba", "immigrants from korea": "Count_Person_Foreign<PERSON>orn_FromKorea", "immigrants from dominican republic": "Count_Person_ForeignBorn_FromDominicanRepublic", "immigrants from guatemala": "Count_Person_ForeignBorn_FromGuatemala", "percentage from mexico": "Percent_Person_ForeignBorn_FromMexico", "percentage from china": "Percent_Person_<PERSON><PERSON><PERSON>_FromChina", "percentage from india": "Percent_Person_ForeignBorn_FromIndia", "male immigrants": "Count_Person_ForeignBorn_Male", "female immigrants": "Count_Person_ForeignBorn_Female", "male immigrant percentage": "Percent_Person_ForeignBorn_Male", "female immigrant percentage": "Percent_Person_ForeignBorn_Female", "child immigrants": "Count_Person_ForeignBorn_Under18Years", "working-age immigrants": "Count_Person_ForeignBorn_18To64Years", "senior immigrants": "Count_Person_ForeignBorn_65O<PERSON><PERSON><PERSON><PERSON><PERSON>s", "child immigrant percentage": "Percent_Person_ForeignBorn_Under18Years", "working-age immigrant percentage": "Percent_Person_ForeignBorn_18To64Years", "senior immigrant percentage": "Percent_Person_ForeignBorn_65OrMoreYears", "employed immigrants": "Count_Person_ForeignBorn_Employed", "immigrant employment rate": "Percent_Person_ForeignBorn_Employed", "unemployed immigrants": "Count_Person_ForeignBorn_Unemployed", "immigrant unemployment rate": "Percent_Person_ForeignBorn_Unemployed", "immigrants in labor force": "Count_Person_ForeignBorn_InLaborForce", "immigrant labor force participation": "Percent_Person_ForeignBorn_InLaborForce", "immigrants with health insurance": "Count_Person_ForeignBorn_WithHealthInsurance", "immigrant health insurance rate": "Percent_Person_ForeignBorn_WithHealthInsurance", "immigrants without health insurance": "Count_Person_ForeignBorn_NoHealthInsurance", "uninsured immigrant rate": "Percent_Person_ForeignBorn_NoHealthInsurance", "immigrants below poverty line": "Count_Person_ForeignBorn_BelowPovertyLevel", "immigrant poverty rate": "Percent_Person_ForeignBorn_BelowPovertyLevel", "immigrants with bachelor's degree": "Count_Person_ForeignBorn_EducationalAttainmentBachelorsDegree", "immigrant bachelor's degree rate": "Percent_Person_ForeignBorn_EducationalAttainmentBachelorsDegree", "immigrants with high school": "Count_Person_ForeignBorn_EducationalAttainmentHighSchool", "immigrant high school rate": "Percent_Person_ForeignBorn_EducationalAttainmentHighSchool", "english-only speaking immigrants": "Count_Person_Foreign<PERSON>orn_SpeakEnglishOnly", "immigrants speaking english very well": "Count_Person_<PERSON><PERSON>orn_SpeakEnglishVeryWell", "immigrants speaking english well": "Count_Person_<PERSON><PERSON><PERSON>_SpeakEnglishWell", "immigrants speaking english not well": "Count_Person_Foreign<PERSON>orn_SpeakEnglishNotWell", "immigrants not speaking english": "Count_Person_ForeignBorn_SpeakEnglishNotAtAll", "english-only immigrant percentage": "Percent_Person_Foreign<PERSON>orn_SpeakEnglishOnly", "english very well immigrant percentage": "Percent_Person_<PERSON><PERSON>orn_SpeakEnglishVeryWell", "limited english proficiency immigrant rate": "Percent_Person_ForeignBorn_LimitedEnglishProficiency", "refugee population": "Count_Person_Refugee", "refugee percentage": "Percent_Person_Refugee", "asylum seeker population": "Count_Person_AsylumSeeker", "daca recipients": "Count_Person_DACA", "tps (temporary protected status) holders": "Count_Person_TPS", "green card holders": "Count_Person_GreenCardHolder", "temporary visa holders": "Count_Person_TemporaryVisa", "undocumented population estimate": "Count_Person_Undocumented", "mixed-status households": "Count_Household_MixedStatus", "all-citizen households": "Count_Household_AllCitizen", "non-citizen households": "Count_Household_NonCitizen", "mixed-status household percentage": "Percent_Household_MixedStatus", "all-citizen household percentage": "Percent_Household_AllCitizen", "non-citizen household percentage": "Percent_Household_NonCitizen", "married with spouse present": "Count_Person_MarriedSpousePresent", "married with spouse absent": "Count_Person_MarriedSpouseAbsent", "divorced population": "Count_Person_Divorced", "widowed population": "Count_Person_Widowed", "never married population": "Count_Person_<PERSON><PERSON><PERSON><PERSON>", "separated population": "Count_Person_Separated", "median personal income": "Median_Income_Person", "median household income": "Median_Income_Household", "mean household income": "Mean_Income_Household", "median family income": "Median_Income_Family", "per capita income": "Per_Capita_Income", "median earnings": "Median_Earnings_Person", "median earnings for full-time male workers": "Median_Earnings_Person_Male_FullTime", "median earnings for full-time female workers": "Median_Earnings_Person_Female_FullTime", "households with income < $10k": "Count_Household_IncomeOfUpto10000USDollar", "households $10k-$14999": "Count_Household_IncomeOf10000To14999USDollar", "households $15k-$24999": "Count_Household_IncomeOf15000To24999US<PERSON>ollar", "households $25k-$34999": "Count_Household_IncomeOf25000To34999US<PERSON>ollar", "households $35k-$49999": "Count_Household_IncomeOf35000To49999US<PERSON><PERSON>ar", "households $50k-$74999": "Count_Household_IncomeOf50000To74999USDollar", "households $75k-$99999": "Count_Household_IncomeOf75000To99999US<PERSON><PERSON>ar", "households $100k-$149999": "Count_Household_IncomeOf100000To149999US<PERSON>ollar", "households $150k-$199999": "Count_Household_IncomeOf150000To199999US<PERSON><PERSON>ar", "households $200k+": "Count_Household_IncomeOf200000OrMoreUSDollar", "people below poverty line": "Count_Person_BelowPovertyLevelInThePast12Months", "poverty rate": "Percent_Person_BelowPovertyLevelInThePast12Months", "families below poverty line": "Count_Family_BelowPovertyLevelInThePast12Months", "people above poverty line": "Count_Person_AbovePovertyLevelInThePast12Months", "children under 5 in poverty": "Count_Person_Upto5Years_BelowPovertyLevelInThePast12Months", "seniors in poverty": "Count_Person_65OrMoreYears_BelowPovertyLevelInThePast12Months", "family poverty rate": "Percent_Family_BelowPovertyLevelInThePast12Months", "males below poverty line": "Count_Person_Male_BelowPovertyLevelInThePast12Months", "females below poverty line": "Count_Person_Female_BelowPovertyLevelInThePast12Months", "male poverty rate": "Percent_Person_Male_BelowPovertyLevelInThePast12Months", "female poverty rate": "Percent_Person_Female_BelowPovertyLevelInThePast12Months", "working-age adults in poverty": "Count_Person_18To64Years_BelowPovertyLevelInThePast12Months", "working-age adult poverty rate": "Percent_Person_18To64Years_BelowPovertyLevelInThePast12Months", "children in poverty": "Count_Person_Under18Years_BelowPovertyLevelInThePast12Months", "child poverty rate": "Percent_Person_Under18Years_BelowPovertyLevelInThePast12Months", "white population in poverty": "Count_Person_WhiteAlone_BelowPovertyLevelInThePast12Months", "white poverty rate": "Percent_Person_WhiteAlone_BelowPovertyLevelInThePast12Months", "black population in poverty": "Count_Person_BlackOrAfricanAmericanAlone_BelowPovertyLevelInThePast12Months", "black poverty rate": "Percent_Person_BlackOrAfricanAmericanAlone_BelowPovertyLevelInThePast12Months", "asian population in poverty": "Count_Person_AsianAlone_BelowPovertyLevelInThePast12Months", "asian poverty rate": "Percent_Person_AsianAlone_BelowPovertyLevelInThePast12Months", "hispanic population in poverty": "Count_Person_HispanicOrLatino_BelowPovertyLevelInThePast12Months", "hispanic poverty rate": "Percent_Person_HispanicOrLatino_BelowPovertyLevelInThePast12Months", "native-born in poverty": "Count_Person_NativeBorn_BelowPovertyLevelInThePast12Months", "native-born poverty rate": "Percent_Person_NativeBorn_BelowPovertyLevelInThePast12Months", "foreign-born in poverty": "Count_Person_ForeignBorn_BelowPovertyLevelInThePast12Months", "foreign-born poverty rate": "Percent_Person_ForeignBorn_BelowPovertyLevelInThePast12Months", "working poor": "Count_Person_<PERSON><PERSON>oor", "working poverty rate": "Percent_Person_Employed_BelowPovertyLevelInThePast12Months", "unemployed in poverty": "Count_Person_Unemployed_BelowPovertyLevelInThePast12Months", "unemployed poverty rate": "Percent_Person_Unemployed_BelowPovertyLevelInThePast12Months", "full-time workers in poverty": "Count_Person_FullTimeWorker_BelowPovertyLevelInThePast12Months", "full-time worker poverty rate": "Percent_Person_FullTimeWorker_BelowPovertyLevelInThePast12Months", "part-time workers in poverty": "Count_Person_PartTimeWorker_BelowPovertyLevelInThePast12Months", "part-time worker poverty rate": "Percent_Person_PartTimeWorker_BelowPovertyLevelInThePast12Months", "people with disabilities in poverty": "Count_Person_DisabilityAndPoverty", "disability poverty rate": "Percent_Person_WithDisability_BelowPovertyLevelInThePast12Months", "single mothers in poverty": "Count_Person_SingleMother_BelowPovertyLevelInThePast12Months", "single mother poverty rate": "Percent_Person_SingleMother_BelowPovertyLevelInThePast12Months", "single fathers in poverty": "Count_Person_SingleFather_BelowPovertyLevelInThePast12Months", "single father poverty rate": "Percent_Person_SingleFather_BelowPovertyLevelInThePast12Months", "households below poverty line": "Count_Household_BelowPovertyLevelInThePast12Months", "household poverty rate": "Percent_Household_BelowPovertyLevelInThePast12Months", "deep poverty (below 50% poverty line)": "Count_Person_Below50PercentPovertyLevel", "deep poverty rate": "Percent_Person_Below50PercentPovertyLevel", "near poverty (below 125% poverty line)": "Count_Person_Below125PercentPovertyLevel", "near poverty rate": "Percent_Person_Below125PercentPovertyLevel", "below 150% poverty line": "Count_Person_Below150PercentPovertyLevel", "below 150% poverty rate": "Percent_Person_Below150PercentPovertyLevel", "below 200% poverty line": "Count_Person_Below200PercentPovertyLevel", "below 200% poverty rate": "Percent_Person_Below200PercentPovertyLevel", "gini index of income inequality": "GiniIndex_EconomicActivity", "nominal gdp": "Amount_EconomicActivity_GrossDomesticProduction_Nominal", "real gdp": "Amount_EconomicActivity_GrossDomesticProduction_RealValue", "gdp per capita": "Amount_EconomicActivity_GrossDomesticProduction_Nominal_PerCapita", "gdp growth rate": "GrowthRate_Amount_EconomicActivity_GrossDomesticProduction", "employed population": "Count_Person_Employed", "unemployed population": "Count_Person_Unemployed", "labor force population": "Count_Person_InLaborF<PERSON>ce", "not in labor force": "Count_Person_NotInLaborForce", "unemployment rate": "UnemploymentRate_Person", "labor force participation rate": "LaborForceParticipationRate_Person", "employment rate": "EmploymentRate_Person", "employed males": "Count_Person_Male_Employed", "employed females": "Count_Person_Female_Employed", "male unemployment rate": "UnemploymentRate_Person_Male", "female unemployment rate": "UnemploymentRate_Person_Female", "employed in agriculture": "Count_Person_Employed_Agriculture", "employed in construction": "Count_Person_Employed_Construction", "employed in manufacturing": "Count_Person_Employed_Manufacturing", "employed in retail": "Count_Person_Employed_Retail", "employed in transportation": "Count_Person_Employed_Transportation", "employed in information sector": "Count_Person_Employed_Information", "employed in finance": "Count_Person_Employed_Finance", "employed in professional services": "Count_Person_Employed_ProfessionalServices", "employed in education": "Count_Person_Employed_Education", "employed in healthcare": "Count_Person_Employed_Healthcare", "government employees": "Count_Person_Employed_Government", "average commute time": "Mean_CommuteTime_Person", "people working from home": "Count_Person_WorkAtHome", "commuting by car/truck/van": "Count_Person_CommuteByCarTruckOrVan", "commuting by public transit": "Count_Person_CommuteByPublicTransit", "walking to work": "Count_Person_CommuteByWalking", "bicycling to work": "Count_Person_CommuteByBicycle", "no schooling completed": "Count_Person_EducationalAttainmentNoSchoolingCompleted", "less than high school": "Count_Person_EducationalAttainmentLessThanHighSchool", "high school graduate": "Count_Person_EducationalAttainmentHighSchoolGraduate", "some college": "Count_Person_EducationalAttainmentSomeCollege", "associate degree": "Count_Person_EducationalAttainmentAssociateDegree", "bachelor's degree": "Count_Person_EducationalAttainmentBachelorsDegree", "master's degree": "Count_Person_EducationalAttainmentMastersDegree", "professional degree": "Count_Person_EducationalAttainmentProfessionalDegree", "doctorate degree": "Count_Person_EducationalAttainmentDoctorateDegree", "total school enrollment": "Count_Person_EnrolledInSchool", "nursery school enrollment": "Count_Person_EnrolledInNurserySchool", "kindergarten enrollment": "Count_Person_EnrolledInKindergarten", "elementary school enrollment": "Count_Person_EnrolledInElementarySchool", "high school enrollment": "Count_Person_EnrolledInHighSchool", "college enrollment": "Count_Person_EnrolledInCollege", "graduate school enrollment": "Count_Person_EnrolledInGraduateSchool", "number of schools": "Count_School", "number of teachers": "Count_Teacher", "student-teacher ratio": "StudentTeacherRatio", "high school graduation rate": "GraduationRate_Person_HighSchool", "public school enrollment": "Count_Person_EnrolledInPublicSchool", "private school enrollment": "Count_Person_EnrolledInPrivateSchool", "people with health insurance": "Count_Person_WithHealthInsurance", "people without health insurance": "Count_Person_NoHealthInsurance", "private health insurance coverage": "Count_Person_WithPrivateHealthInsurance", "public health insurance coverage": "Count_Person_WithPublicHealthInsurance", "medicaid coverage": "Count_Person_WithMedicaid", "medicare coverage": "Count_Person_WithMedicare", "households without health insurance": "Count_Household_NoHealthInsurance", "life expectancy at birth": "LifeExpectancy_Person", "male life expectancy": "LifeExpectancy_Person_Male", "female life expectancy": "LifeExpectancy_Person_Female", "overall mortality rate": "MortalityRate_Person", "infant mortality rate": "InfantMortalityRate_Person", "mortality rate under 1 year": "MortalityRate_Person_Upto1Year", "under-5 mortality rate": "MortalityRate_Person_Upto5Years", "diabetes prevalence": "Prevalence_Diabetes", "obesity prevalence": "Prevalence_Obesity", "heart disease prevalence": "Prevalence_HeartDisease", "cancer prevalence": "Prevalence_Cancer", "asthma prevalence": "Prevalence_Asthma", "copd prevalence": "Prevalence_COPD", "hypertension prevalence": "Prevalence_Hypertension", "depression prevalence": "Prevalence_Depression", "percentage of population with diabetes": "Percent_Person_Diabetes", "percentage of population obese": "Percent_Person_Obesity", "percentage with heart disease": "Percent_Person_HeartDisease", "percentage with hypertension": "Percent_Person_Hypertension", "percentage with high cholesterol": "Percent_Person_HighCholesterol", "percentage with asthma": "Percent_Person_Asthma", "percentage with copd": "Percent_Person_COPD", "percentage with cancer": "Percent_Person_Cancer", "percentage with kidney disease": "Percent_<PERSON>_KidneyDisease", "percentage with arthritis": "Percent_Person_Arthritis", "percentage with depression": "Percent_Person_Depression", "percentage with anxiety disorders": "Percent_Person_Anxiety", "stroke prevalence": "Prevalence_Stroke", "percentage with stroke history": "Percent_Person_Stroke", "coronary heart disease prevalence": "Prevalence_CoronaryHeartDisease", "percentage with coronary heart disease": "Percent_Person_CoronaryHeartDisease", "heart attack prevalence": "Prevalence_HeartAttack", "percentage with heart attack history": "Percent_Person_HeartAttack", "kidney disease prevalence": "Prevalence_KidneyDisease", "chronic kidney disease prevalence": "Prevalence_ChronicKidneyDisease", "percentage with chronic kidney disease": "Percent_Person_ChronicKidneyDisease", "arthritis prevalence": "Prevalence_Arthritis", "alzheimer's disease prevalence": "Prevalence_Alzheimer", "percentage with alzheimer's disease": "Percent_Person_Alzheimer", "dementia prevalence": "Prevalence_Dementia", "percentage with dementia": "Percent_Person_Dementia", "autism spectrum disorder prevalence": "Prevalence_Autism", "percentage with autism spectrum disorder": "Percent_Person_Autism", "adhd prevalence": "Prevalence_ADHD", "percentage with adhd": "Percent_Person_ADHD", "substance abuse prevalence": "Prevalence_SubstanceAbuse", "percentage with substance abuse": "Percent_Person_SubstanceAbuse", "alcohol abuse prevalence": "Prevalence_AlcoholAbuse", "percentage with alcohol abuse": "Percent_Person_AlcoholAbuse", "drug abuse prevalence": "Prevalence_DrugAbuse", "percentage with drug abuse": "Percent_Person_DrugAbuse", "mental illness prevalence": "Prevalence_MentalIllness", "percentage with mental illness": "Percent_Person_MentalIllness", "bipolar disorder prevalence": "Prevalence_BipolarDisorder", "percentage with bipolar disorder": "Percent_Person_BipolarDisorder", "schizophrenia prevalence": "Prevalence_Schizophrenia", "percentage with schizophrenia": "Percent_Person_Schizophrenia", "ptsd prevalence": "Prevalence_PTSD", "percentage with ptsd": "Percent_Person_PTSD", "eating disorder prevalence": "Prevalence_EatingDisorder", "percentage with eating disorders": "Percent_Person_EatingDisorder", "liver disease prevalence": "Prevalence_LiverDisease", "percentage with liver disease": "Percent_Person_LiverDisease", "hepatitis prevalence": "Prevalence_Hepatitis", "percentage with hepatitis b": "Percent_Person_HepatitisB", "percentage with hepatitis c": "Percent_Person_HepatitisC", "hiv prevalence": "Prevalence_HIV", "percentage with hiv": "Percent_Person_HIV", "aids prevalence": "Prevalence_AIDS", "percentage with aids": "Percent_Person_AIDS", "sexually transmitted disease prevalence": "Prevalence_STD", "percentage with stds": "Percent_Person_STD", "tuberculosis prevalence": "Prevalence_Tuberculosis", "percentage with tuberculosis": "Percent_<PERSON>_Tuberculosis", "pneumonia prevalence": "Prevalence_Pneumonia", "percentage with pneumonia": "Percent_Person_Pneumonia", "influenza prevalence": "Prevalence_Influenza", "percentage with influenza": "Percent_Person_Influenza", "bronchitis prevalence": "Prevalence_Bronchitis", "percentage with bronchitis": "Percent_<PERSON>_<PERSON><PERSON><PERSON>", "emphysema prevalence": "Prevalence_Emphysema", "percentage with emphysema": "Percent_Person_Emphysema", "lung disease prevalence": "Prevalence_LungDisease", "percentage with lung disease": "Percent_Person_LungDisease", "sleep apnea prevalence": "Prevalence_SleepApnea", "percentage with sleep apnea": "Percent_Person_SleepApnea", "insomnia prevalence": "Prevalence_Insomnia", "percentage with insomnia": "Percent_Person_Insomnia", "chronic pain prevalence": "Prevalence_ChronicPain", "percentage with chronic pain": "Percent_Person_ChronicPain", "back pain prevalence": "Prevalence_BackPain", "percentage with back pain": "Percent_<PERSON>_BackPain", "migraine prevalence": "Prevalence_Migraine", "percentage with migraines": "Percent_<PERSON>_Migraine", "osteoporosis prevalence": "Prevalence_Osteoporosis", "percentage with osteoporosis": "Percent_<PERSON>_Osteoporosis", "fibromyalgia prevalence": "Prevalence_Fibromyalgia", "percentage with fibromyalgia": "Percent_Person_Fibromyalgia", "lupus prevalence": "Prevalence_Lupus", "percentage with lupus": "Percent_<PERSON>_<PERSON><PERSON>", "multiple sclerosis prevalence": "Prevalence_MultipleSclerosis", "percentage with multiple sclerosis": "Percent_Person_MultipleSclerosis", "parkinson's disease prevalence": "Prevalence_Parkinson", "percentage with parkinson's disease": "Percent_<PERSON>_<PERSON>", "epilepsy prevalence": "Prevalence_Epilepsy", "percentage with epilepsy": "Percent_Person_Epilepsy", "thyroid disease prevalence": "Prevalence_ThyroidDisease", "percentage with thyroid disease": "Percent_Person_ThyroidDisease", "hypothyroidism prevalence": "Prevalence_Hypothyroidism", "percentage with hypothyroidism": "Percent_Person_Hypothyroidism", "hyperthyroidism prevalence": "Prevalence_Hyperthyroidism", "percentage with hyperthyroidism": "Percent_Person_Hyperthyroidism", "irritable bowel syndrome prevalence": "Prevalence_IBS", "percentage with ibs": "Percent_Person_IBS", "crohn's disease prevalence": "Prevalence_<PERSON><PERSON>hn", "percentage with crohn's disease": "Percent_<PERSON>_<PERSON>", "ulcerative colitis prevalence": "Prevalence_UlcerativeColitis", "percentage with ulcerative colitis": "Percent_Person_UlcerativeColitis", "celiac disease prevalence": "Prevalence_Celiac", "percentage with celiac disease": "Percent_<PERSON>_<PERSON>c", "food allergy prevalence": "Prevalence_FoodAllergy", "percentage with food allergies": "Percent_Person_FoodAllergy", "environmental allergy prevalence": "Prevalence_EnvironmentalAllergy", "percentage with environmental allergies": "Percent_Person_EnvironmentalAllergy", "eczema prevalence": "Prevalence_Eczema", "percentage with eczema": "Percent_<PERSON>_<PERSON>ema", "psoriasis prevalence": "Prevalence_Psoriasis", "percentage with psoriasis": "Percent_<PERSON>_Psoriasis", "acne prevalence": "Prevalence_AcneVulgaris", "percentage with acne": "Percent_<PERSON>_AcneVulgaris", "rheumatoid arthritis prevalence": "Prevalence_RheumatoidArthritis", "percentage with rheumatoid arthritis": "Percent_Person_RheumatoidA<PERSON>ritis", "gout prevalence": "Prevalence_Gout", "percentage with gout": "Percent_Person_Gout", "anemia prevalence": "Prevalence_Anemia", "percentage with anemia": "Percent_Person_Anemia", "sickle cell disease prevalence": "Prevalence_SickleCellDisease", "percentage with sickle cell disease": "Percent_<PERSON>_SickleCellDisease", "hemophilia prevalence": "Prevalence_Hemophilia", "percentage with hemophilia": "Percent_Person_Hemophilia", "blood clotting disorder prevalence": "Prevalence_BloodClottingDisorder", "percentage with blood clotting disorders": "Percent_Person_BloodClottingDisorder", "vision impairment prevalence": "Prevalence_VisionImpairment", "percentage with vision impairment": "Percent_Person_VisionImpairment", "blindness prevalence": "Prevalence_Blindness", "percentage blind": "Percent_Person_Blindness", "glaucoma prevalence": "Prevalence_Glaucoma", "percentage with glaucoma": "Percent_<PERSON>_Glaucoma", "cataracts prevalence": "Prevalence_Cataracts", "percentage with cataracts": "Percent_Person_Cataracts", "macular degeneration prevalence": "Prevalence_MacularDegeneration", "percentage with macular degeneration": "Percent_Person_MacularDegeneration", "hearing loss prevalence": "Prevalence_HearingLoss", "percentage with hearing loss": "Percent_Person_HearingLoss", "deafness prevalence": "Prevalence_Deafness", "percentage deaf": "Percent_Person_Deafness", "tinnitus prevalence": "Prevalence_Tinnitus", "percentage with tinnitus": "Percent_<PERSON>_Tinnitus", "peripheral neuropathy prevalence": "Prevalence_PeripheralNeuropathy", "percentage with peripheral neuropathy": "Percent_Person_PeripheralNeuropathy", "chronic fatigue syndrome prevalence": "Prevalence_ChronicFatigueSyndrome", "percentage with chronic fatigue syndrome": "Percent_Person_ChronicFatigueSyndrome", "autoimmune disease prevalence": "Prevalence_AutoimmuneDisease", "percentage with autoimmune disease": "Percent_Person_AutoimmuneDisease", "genetic disorder prevalence": "Prevalence_GeneticDisorder", "percentage with genetic disorders": "Percent_Person_GeneticDisorder", "congenital anomalies prevalence": "Prevalence_CongenitalAnomalies", "percentage with congenital anomalies": "Percent_Person_CongenitalAnomalies", "developmental disability prevalence": "Prevalence_DevelopmentalDisability", "percentage with developmental disabilities": "Percent_Person_DevelopmentalDisability", "intellectual disability prevalence": "Prevalence_IntellectualDisability", "percentage with intellectual disabilities": "Percent_Person_IntellectualDisability", "learning disability prevalence": "Prevalence_LearningDisability", "percentage with learning disabilities": "Percent_Person_LearningDisability", "disability and poverty rate": "Percent_Person_DisabilityAndPoverty", "immigrants with disabilities": "Count_Person_ForeignBornWithDisability", "immigrant disability rate": "Percent_Person_ForeignBornWithDisability", "elderly immigrants": "Count_Person_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elderly immigrant rate": "Percent_Person_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elderly in poverty": "Count_Person_<PERSON><PERSON><PERSON><PERSON><PERSON>", "elderly poverty rate": "Percent_Person_Elder<PERSON><PERSON><PERSON><PERSON>", "elderly with disabilities": "Count_Person_ElderlyDisability", "elderly disability rate": "Percent_Person_ElderlyDisability", "elderly living alone": "Count_Person_ElderlyLiving<PERSON>lone", "elderly living alone rate": "Percent_Person_ElderlyLivingAlone", "socially isolated elderly": "Count_Person_ElderlyIsolated", "elderly isolation rate": "Percent_Person_ElderlyIsolated", "total vulnerable population": "Count_Person_VulnerablePopulation", "vulnerable population rate": "Percent_Person_VulnerablePopulation", "disadvantaged minorities": "Count_Person_DisadvantagedMinority", "disadvantaged minority rate": "Percent_Person_DisadvantagedMinority", "linguistically isolated": "Count_Person_LinguisticallyIsolated", "linguistic isolation rate": "Percent_Person_LinguisticallyIsolated", "linguistically isolated households": "Count_Household_LinguisticallyIsolated", "linguistically isolated household rate": "Percent_Household_LinguisticallyIsolated", "migrant workers": "Count_Person_MigrantW<PERSON><PERSON>", "migrant worker rate": "Percent_<PERSON>_MigrantWorker", "seasonal workers": "Count_Person_SeasonalWorker", "seasonal worker rate": "Percent_Person_SeasonalWorker", "undocumented workers": "Count_Person_Undocumented<PERSON><PERSON><PERSON>", "undocumented worker rate": "Percent_Person_UndocumentedWorker", "day laborers": "Count_Person_Day<PERSON><PERSON><PERSON>r", "day laborer rate": "Percent_Person_DayLaborer", "guest workers (h-2a/h-2b)": "Count_Person_<PERSON><PERSON><PERSON><PERSON>", "guest worker rate": "Percent_Person_Guest<PERSON><PERSON><PERSON>", "international students": "Count_Person_InternationalStudent", "international student rate": "Percent_Person_InternationalStudent", "h-1b visa holders": "Count_Person_H1BVisa", "remittance senders": "Count_Person_RemittanceSender", "total remittance outflow": "RemittanceOutflow", "remittance per capita": "RemittancePerCapita", "first generation immigrants": "Count_Person_FirstGeneration", "second generation (children of immigrants)": "Count_Person_SecondGeneration", "third generation or higher": "Count_Person_ThirdGeneration<PERSON><PERSON><PERSON><PERSON><PERSON>", "first generation percentage": "Percent_Person_FirstGeneration", "second generation percentage": "Percent_Person_SecondGeneration", "third generation+ percentage": "Percent_Person_ThirdGeneration<PERSON><PERSON><PERSON><PERSON><PERSON>", "people in mixed-citizenship households": "Count_Person_MixedCitizenship_Household", "mixed-citizenship household rate": "Percent_Person_MixedCitizenship_Household", "us-born children with foreign-born parents": "Count_Child_<PERSON><PERSON>_ForeignBornParents", "us-born children with immigrant parents rate": "Percent_<PERSON>_<PERSON><PERSON>_ForeignBornParents", "unaccompanied minors": "Count_Person_Unaccompanied<PERSON><PERSON><PERSON>", "separated families": "Count_Person_SeparatedFamily", "family reunification cases": "Count_Person_FamilyReunification", "naturalization rate": "NaturalizationRate", "eligible to naturalize": "Count_Person_EligibleToNaturalize", "naturalization eligibility rate": "Percent_Person_EligibleToNaturalize", "pending immigration cases": "Count_Person_PendingImmigrationCase", "immigration court backlog": "ImmigrationCourtBacklog", "physical disability prevalence": "Count_Person_PhysicalDisability", "percentage with physical disabilities": "Percent_Person_PhysicalDisability", "mobility impairment prevalence": "Count_Person_MobilityImpairment", "percentage with mobility impairment": "Percent_Person_MobilityImpairment", "economic mobility index": "EconomicMobilityIndex", "intergenerational mobility rate": "IntergenerationalMobilityRate", "moved out of poverty": "Count_Person_MovedOutOfPoverty", "poverty exit rate": "Percent_Person_MovedOutOfPoverty", "fell into poverty": "Count_Person_Fell<PERSON>nto<PERSON><PERSON><PERSON>", "poverty entry rate": "Percent_Person_FellIntoPoverty", "median years in poverty": "MedianYearsInPoverty", "chronic poverty (3+ years)": "Count_Person_ChronicPoverty", "chronic poverty rate": "Percent_Person_ChronicPoverty", "recurrent poverty": "Count_Person_RecurrentPoverty", "recurrent poverty rate": "Percent_Person_RecurrentPoverty", "near poor population": "Count_Person_<PERSON>", "near poor rate": "Percent_<PERSON>_<PERSON>", "economically vulnerable": "Count_Person_EconomicallyVulnerable", "economic vulnerability rate": "Percent_Person_EconomicallyVulnerable", "financially fragile": "Count_Person_FinanciallyFragile", "financial fragility rate": "Percent_Person_FinanciallyFragile", "no emergency savings": "Count_Person_NoEmergencySavings", "no emergency savings rate": "Percent_Person_NoEmergencySavings", "unbanked or underbanked": "Count_Person_UnbankedOrUnderbanked", "unbanked/underbanked rate": "Percent_Person_UnbankedOrUnderbanked", "credit invisible": "Count_Person_CreditInvisible", "credit invisible rate": "Percent_Person_CreditInvisible", "subprime credit": "Count_Person_SubprimeCredit", "subprime credit rate": "Percent_Person_SubprimeCredit", "debt burdened": "Count_Person_DebtBurdened", "debt burden rate": "Percent_Person_DebtBurdened", "student debt burdened": "Count_Person_StudentDeb<PERSON><PERSON><PERSON><PERSON>", "student debt burden rate": "Percent_Person_StudentDebtBurdened", "medical debt burdened": "Count_Person_MedicalD<PERSON><PERSON><PERSON><PERSON><PERSON>", "medical debt burden rate": "Percent_Person_MedicalDebtBurdened", "social capital index": "SocialCapitalIndex", "socially disconnected": "Count_Person_SociallyDisconnected", "social disconnection rate": "Percent_Person_SociallyDisconnected", "opportunity index": "OpportunityIndex", "opportunity youth (16-24 not in school/work)": "Count_Person_OpportunityYouth", "opportunity youth rate": "Percent_Person_OpportunityYouth", "disconnected youth": "Count_Person_DisconnectedYouth", "disconnected youth rate": "Percent_Person_DisconnectedYouth", "neet (not in education employment or training)": "Count_Person_NEET", "neet rate": "Percent_Person_NEET", "number of medical practitioners": "Count_MedicalPractitioner", "percentage who smoke": "Percent_Person_Smoker", "percentage former smokers": "Percent_Person_Former<PERSON><PERSON><PERSON>", "percentage never smoked": "Percent_<PERSON>_<PERSON>Smo<PERSON>", "percentage current smokers": "Percent_Person_CurrentSmoker", "percentage heavy smokers": "Percent_Person_HeavySmoker", "percentage using e-cigarettes": "Percent_Person_ECigaretteUser", "percentage using tobacco products": "Percent_Person_TobaccoUser", "percentage who drink alcohol": "Percent_Person_AlcoholConsumer", "percentage binge drinkers": "Percent_<PERSON>_BingeDrinker", "percentage heavy drinkers": "Percent_Person_HeavyDrinker", "percentage physically active": "Percent_Person_PhysicallyActive", "percentage physically inactive": "Percent_Person_PhysicallyInactive", "percentage with sedentary lifestyle": "Percent_Person_SedentaryLifestyle", "percentage meeting exercise guidelines": "Percent_Person_MeetsExerciseGuidelines", "percentage overweight": "Percent_Person_Overweight", "percentage underweight": "Percent_Person_Underweight", "percentage normal weight": "Percent_Person_NormalWeight", "percentage morbidly obese": "Percent_Person_MorbidlyObese", "percentage pre-diabetic": "Percent_Person_PreDiabetic", "percentage with type 1 diabetes": "Percent_Person_Type1Diabetes", "percentage with type 2 diabetes": "Percent_Person_Type2Diabetes", "percentage with gestational diabetes": "Percent_Person_GestationalDiabetes", "percentage with metabolic syndrome": "Percent_Person_MetabolicSyndrome", "percentage with high blood pressure": "Percent_Person_HighBloodPressure", "percentage with pre-hypertension": "Percent_Person_PreHypertension", "percentage with high blood sugar": "Percent_Person_HighBloodSugar", "percentage with low blood sugar": "Percent_Person_LowBloodSugar", "percentage with vitamin d deficiency": "Percent_Person_VitaminDDeficiency", "percentage with iron deficiency": "Percent_Person_IronDeficiency", "percentage with b12 deficiency": "Percent_Person_B12Deficiency", "percentage with calcium deficiency": "Percent_Person_CalciumDeficiency", "percentage malnourished": "Percent_Person_Malnutrition", "percentage food insecure": "Percent_Person_FoodInsecurity", "percentage with regular medical checkup": "Percent_Person_WithRegularCheckup", "percentage with primary care provider": "Percent_Person_WithPrimaryCareProvider", "percentage who delayed medical care": "Percent_Person_DelayedMedicalCare", "percentage with unmet medical needs": "Percent_Person_UnmetMedicalNeed", "percentage using prescription drugs": "Percent_Person_PrescriptionDrugUse", "percentage using opioids": "Percent_Person_OpioidUse", "percentage using antidepressants": "Percent_Person_AntidepressantUse", "percentage using anxiety medication": "Percent_Person_AnxietyMedicationUse", "percentage using pain medication": "Percent_Person_PainMedicationUse", "percentage with flu vaccine": "Percent_Person_VaccinatedFlu", "percentage with pneumonia vaccine": "Percent_Person_VaccinatedPneumonia", "percentage with shingles vaccine": "Percent_Person_VaccinatedShingles", "percentage with hpv vaccine": "Percent_Person_VaccinatedHPV", "percentage with hepatitis vaccine": "Percent_Person_VaccinatedHepatitis", "percentage with measles vaccine": "Percent_Person_VaccinatedMeasles", "percentage fully vaccinated": "Percent_Person_FullyVaccinated", "percentage receiving cancer screening": "Percent_Person_CancerScreening", "percentage receiving mammograms": "Percent_Person_MammogramScreening", "percentage receiving colonoscopy": "Percent_Person_ColonoscopyScreening", "percentage receiving pap smears": "Percent_Person_PapSmearScreening", "percentage receiving prostate screening": "Percent_Person_ProstateScreening", "percentage with dental visits": "Percent_Person_DentalVisit", "percentage with eye exams": "Percent_Person_EyeExam", "percentage with hearing tests": "Percent_Person_HearingTest", "percentage receiving mental health treatment": "Percent_Person_MentalHealthTreatment", "percentage receiving substance abuse treatment": "Percent_Person_SubstanceAbuseTreatment", "percentage receiving preventive care": "Percent_Person_PreventiveCare", "percentage with er visits": "Percent_Person_EmergencyRoomVisit", "percentage hospitalized": "Percent_Person_Hospitalized", "percentage with surgery past year": "Percent_Person_SurgeryPastYear", "number of hospitals": "Count_Hospital", "number of hospital beds": "Count_HospitalBed", "physicians per capita": "Count_Physician<PERSON>er<PERSON><PERSON><PERSON>", "nurses per capita": "Count_Nurse<PERSON><PERSON><PERSON><PERSON><PERSON>", "cumulative covid cases": "CumulativeCount_MedicalConditionIncident_COVID_19_ConfirmedCase", "cumulative covid deaths": "CumulativeCount_MedicalConditionIncident_COVID_19_Death", "daily new covid cases": "Count_MedicalConditionIncident_COVID_19_ConfirmedCase", "daily covid deaths": "Count_MedicalConditionIncident_COVID_19_Death", "people vaccinated for covid-19": "CumulativeCount_Person_Vaccinated_COVID_19", "covid vaccination rate": "Percent_Person_Vaccinated_COVID_19", "percentage of males with diabetes": "Percent_Person_Male_Diabetes", "percentage of females with diabetes": "Percent_Person_Female_Diabetes", "percentage of adults with diabetes": "Percent_Person_Adult_Diabetes", "percentage of children with diabetes": "Percent_Person_Child_Diabetes", "percentage of seniors with diabetes": "Percent_Person_65Or<PERSON><PERSON><PERSON><PERSON>s_Diabetes", "percentage of males obese": "Percent_Person_Male_Obesity", "percentage of females obese": "Percent_Person_Female_Obesity", "percentage of adults obese": "Percent_Person_Adult_Obesity", "percentage of children obese": "Percent_Person_Child_Obesity", "percentage of adolescents obese": "Percent_Person_Adolescent_Obesity", "percentage of males with heart disease": "Percent_Person_Male_HeartDisease", "percentage of females with heart disease": "Percent_Person_Female_HeartDisease", "percentage of seniors with heart disease": "Percent_Person_65Or<PERSON><PERSON>Years_HeartDisease", "percentage of males with cancer": "Percent_Person_Male_Cancer", "percentage of females with cancer": "Percent_Person_Female_Cancer", "percentage of adults with asthma": "Percent_Person_Adult_Asthma", "percentage of children with asthma": "Percent_Person_Child_Asthma", "percentage of males with depression": "Percent_Person_Male_Depression", "percentage of females with depression": "Percent_Person_Female_Depression", "percentage of adolescents with depression": "Percent_Person_Adolescent_Depression", "percentage of adults with mental illness": "Percent_Person_Adult_MentalIllness", "percentage of children with mental illness": "Percent_Person_Child_MentalIllness", "percentage of white population with diabetes": "Percent_<PERSON>_<PERSON><PERSON><PERSON>_Diabetes", "percentage of black population with diabetes": "Percent_Person_BlackOrAfricanAmericanAlone_Diabetes", "percentage of asian population with diabetes": "Percent_Person_Asian<PERSON><PERSON>_Diabetes", "percentage of hispanic population with diabetes": "Percent_Person_HispanicOrLatino_Diabetes", "percentage of low-income with diabetes": "Percent_Person_LowIncome_Diabetes", "percentage of low-income obese": "Percent_Person_LowIncome_Obesity", "percentage of low-income uninsured": "Percent_Person_LowIncome_NoHealthInsurance", "cancer incidence rate": "IncidenceRate_Cancer", "breast cancer incidence rate": "IncidenceRate_Cancer_Breast", "lung cancer incidence rate": "IncidenceRate_Cancer_Lung", "prostate cancer incidence rate": "IncidenceRate_Cancer_Prostate", "colorectal cancer incidence rate": "IncidenceRate_Cancer_Colorectal", "skin cancer incidence rate": "IncidenceRate_Cancer_Skin", "pancreatic cancer incidence rate": "IncidenceRate_Cancer_Pancreatic", "liver cancer incidence rate": "IncidenceRate_Cancer_Liver", "leukemia incidence rate": "IncidenceRate_Cancer_Leukemia", "lymphoma incidence rate": "IncidenceRate_Cancer_Lymphoma", "diabetes incidence rate": "IncidenceRate_Diabetes", "heart disease incidence rate": "IncidenceRate_HeartDisease", "stroke incidence rate": "IncidenceRate_Stroke", "hiv incidence rate": "IncidenceRate_HIV", "std incidence rate": "IncidenceRate_STD", "tuberculosis incidence rate": "IncidenceRate_Tuberculosis", "cancer mortality rate": "MortalityRate_Cancer", "heart disease mortality rate": "MortalityRate_HeartDisease", "stroke mortality rate": "MortalityRate_Stroke", "diabetes mortality rate": "MortalityRate_Diabetes", "clrd mortality rate": "MortalityRate_ChronicLowerRespiratoryDisease", "alzheimer's mortality rate": "MortalityRate_Alzheimer", "influenza mortality rate": "MortalityRate_Influenza", "pneumonia mortality rate": "MortalityRate_Pneumonia", "kidney disease mortality rate": "MortalityRate_KidneyDisease", "suicide mortality rate": "MortalityRate_Suicide", "drug overdose mortality rate": "MortalityRate_Overdose", "accidental death rate": "MortalityRate_Accident", "5-year cancer survival rate": "SurvivalRate_Cancer_5Year", "5-year breast cancer survival rate": "SurvivalRate_Cancer_Breast_5Year", "5-year lung cancer survival rate": "SurvivalRate_Cancer_Lung_5Year", "5-year prostate cancer survival rate": "SurvivalRate_Cancer_Prostate_5Year", "disability-adjusted life years (dalys)": "DisabilityAdjustedLifeYears", "quality-adjusted life years (qalys)": "QualityAdjustedLifeYears", "years of potential life lost": "YearsOfPotentialLifeLost", "healthy life expectancy": "HealthyLifeExpectancy", "fertility rate": "FertilityRate_Person_Female", "birth rate": "BirthRate_Person", "number of births": "Count_BirthEvent", "live births": "Count_BirthEvent_LiveBirth", "teen birth rate": "TeenB<PERSON>hRate_Person_Female", "maternal mortality rate": "MaternalMortalityRate", "occupied housing units": "Count_HousingUnit_OccupiedHousingUnit", "vacant housing units": "Count_HousingUnit_VacantHousingUnit", "owner-occupied units": "Count_HousingUnit_OwnerOccupied", "renter-occupied units": "Count_HousingUnit_RenterOccupied", "homeownership rate": "HomeownershipRate", "housing vacancy rate": "VacancyRate_HousingUnit", "median home value": "Median_Value_HousingUnit", "median gross rent": "Median_GrossRent", "median monthly owner costs with mortgage": "Median_SelectedMonthlyOwnerCosts_HousingUnit_WithMortgage", "median monthly owner costs without mortgage": "Median_SelectedMonthlyOwnerCosts_HousingUnit_WithoutMortgage", "mean home value": "Mean_Value_HousingUnit", "single-family detached homes": "Count_HousingUnit_SingleFamilyDetached", "single-family attached homes": "Count_HousingUnit_SingleFamilyAttached", "2-unit buildings": "Count_HousingUnit_MultiFamily2Units", "3-4 unit buildings": "Count_HousingUnit_MultiFamily3To4Units", "5-9 unit buildings": "Count_HousingUnit_MultiFamily5To9Units", "10-19 unit buildings": "Count_HousingUnit_MultiFamily10To19Units", "20+ unit buildings": "Count_HousingUnit_MultiFamily20OrMoreUnits", "mobile homes": "Count_HousingUnit_MobileHome", "housing built before 1940": "Count_HousingUnit_Before1940", "housing built 1940-1949": "Count_HousingUnit_1940To1949", "housing built 1950-1959": "Count_HousingUnit_1950To1959", "housing built 1960-1969": "Count_HousingUnit_1960To1969", "housing built 1970-1979": "Count_HousingUnit_1970To1979", "housing built 1980-1989": "Count_HousingUnit_1980To1989", "housing built 1990-1999": "Count_HousingUnit_1990To1999", "housing built 2000-2009": "Count_HousingUnit_2000To2009", "housing built 2010 or later": "Count_HousingUnit_2010OrLater", "households with housing cost burden": "Count_Household_HousingCostBurden", "households with severe cost burden": "Count_Household_SevereHousingCostBurden", "units lacking complete plumbing": "Count_HousingUnit_LackingCompletePlumbing", "units lacking complete kitchen": "Count_HousingUnit_LackingCompleteKitchen", "overcrowded housing units": "Count_HousingUnit_Overcrowded", "total violent crimes": "Count_CriminalActivities_ViolentCrime", "murders": "Count_CriminalActivities_Murder", "rapes": "Count_CriminalActivities_Rape", "robberies": "Count_CriminalActivities_<PERSON><PERSON>", "aggravated assaults": "Count_CriminalActivities_AggravatedAssault", "total property crimes": "Count_CriminalActivities_PropertyCrime", "burglaries": "Count_CriminalActivities_Burglary", "larcenies": "Count_CriminalActivities_Larceny", "motor vehicle thefts": "Count_CriminalActivities_MotorVehicleTheft", "arson cases": "Count_CriminalActivities_Arson", "violent crime rate per capita": "CrimeRate_Person_ViolentCrime", "property crime rate per capita": "CrimeRate_Person_PropertyCrime", "murder rate per capita": "CrimeRate_Person_Murder", "number of law enforcement officers": "Count_LawEnforcementOfficer", "number of police officers": "Count_PoliceOfficer", "incarcerated population": "Count_Incarceration", "incarceration rate": "IncarcerationRate_Person", "pm2.5 concentration": "Mean_Concentration_AirPollutant_PM2.5", "pm10 concentration": "Mean_Concentration_AirPollutant_PM10", "ozone concentration": "Mean_Concentration_AirPollutant_Ozone", "no2 concentration": "Mean_Concentration_AirPollutant_NO2", "so2 concentration": "Mean_Concentration_AirPollutant_SO2", "co concentration": "Mean_Concentration_AirPollutant_CO", "air quality index": "AirQualityIndex", "mean temperature": "Mean_Temperature", "maximum temperature": "Max_Temperature", "minimum temperature": "Min_Temperature", "summer mean temperature": "Mean_Temperature_Summer", "winter mean temperature": "Mean_Temperature_Winter", "annual precipitation": "Annual_Precipitation", "annual snowfall": "Annual_Snowfall", "annual co2 emissions": "Annual_Emissions_CO2", "co2 emissions per capita": "Annual_Emissions_CO2_PerCapita", "methane emissions": "Annual_Emissions_Methane", "total greenhouse gas emissions": "Annual_Emissions_GreenhouseGas", "co2 from transportation": "Annual_Emissions_CO2_Transportation", "co2 from electricity": "Annual_Emissions_CO2_Electricity", "co2 from industrial sources": "Annual_Emissions_CO2_Industrial", "number of flood events": "Count_FloodEvent", "number of wildfire events": "Count_WildfireEvent", "number of drought events": "Count_<PERSON><PERSON><PERSON><PERSON>", "number of hurricane events": "Count_HurricaneEvent", "number of tornado events": "Count_TornadoEvent", "number of earthquake events": "Count_EarthquakeEvent", "total land area": "Area_Land", "total water area": "Area_Water", "forest area": "Area_Forest", "agricultural land area": "Area_Agricultural", "urban area": "Area_Urban", "protected land area": "Area_ProtectedLand", "total electricity generation": "Annual_Generation_Electricity", "electricity from coal": "Annual_Generation_Electricity_Coal", "electricity from natural gas": "Annual_Generation_Electricity_NaturalGas", "electricity from nuclear": "Annual_Generation_Electricity_Nuclear", "electricity from renewables": "Annual_Generation_Electricity_Renewable", "solar electricity generation": "Annual_Generation_Electricity_Solar", "wind electricity generation": "Annual_Generation_Electricity_Wind", "hydroelectric generation": "Annual_Generation_Electricity_Hydro", "total energy consumption": "Annual_Consumption_Energy", "energy consumption per capita": "Annual_Consumption_Energy_PerCapita", "electricity consumption": "Annual_Consumption_Electricity", "residential electricity use": "Annual_Consumption_Electricity_Residential", "commercial electricity use": "Annual_Consumption_Electricity_Commercial", "industrial electricity use": "Annual_Consumption_Electricity_Industrial", "natural gas consumption": "Annual_Consumption_NaturalGas", "average electricity price": "Quarterly_Average_RetailPrice_Electricity", "average gasoline price": "Average_RetailPrice_Gasoline", "average natural gas price": "Average_RetailPrice_NaturalGas", "residential electricity price": "Average_RetailPrice_Electricity_Residential", "commercial electricity price": "Average_RetailPrice_Electricity_Commercial", "total vehicles": "Count_Vehicle", "number of cars": "Count_Vehicle_Car", "number of trucks": "Count_Vehicle_Truck", "number of motorcycles": "Count_Vehicle_Motorcycle", "vehicle ownership rate": "Vehicle_Ownership_Rate", "households without vehicles": "Count_Household_NoVehicle", "households with 1 vehicle": "Count_Household_1Vehicle", "households with 2 vehicles": "Count_Household_2V<PERSON><PERSON>", "households with 3+ vehicles": "Count_Household_3OrMoreVehicles", "total road length": "Length_Road", "highway length": "Length_Highway", "number of transit stations": "Count_PublicTransitStation", "number of airports": "Count_Airport", "number of rail stations": "Count_RailStation", "vehicle miles traveled": "Annual_VehicleMilesTraveled", "number of traffic accidents": "Count_TrafficAccident", "traffic fatalities": "Count_TrafficFatality", "traffic fatality rate": "TrafficFatalityRate", "traffic injuries": "Count_TrafficInjury", "number of farms": "Count_Farm", "total farm area": "Area_Farm", "average farm size": "Mean_Area_Farm", "agricultural production value": "Value_AgriculturalProduction", "crop farms": "Count_Farm_Crop", "livestock farms": "Count_Farm_Livestock", "organic farms": "Count_Farm_Organic", "corn production": "Production_Corn", "wheat production": "Production_Wheat", "soybean production": "Production_Soybean", "rice production": "Production_Rice", "corn yield": "Yi<PERSON>_<PERSON>rn", "wheat yield": "Yield_Wheat", "soybean yield": "Yield_Soybean", "food insecure population": "Count_Person_FoodInsecure", "food insecurity rate": "FoodInsecurityRate_Person", "snap recipients": "Count_Person_WithSNAPBenefits", "wic recipients": "Count_Person_WithWICBenefits", "number of food deserts": "Count_FoodD<PERSON>rt", "total government revenue": "Annual_Revenue_Government", "tax revenue": "Annual_Revenue_Government_Tax", "property tax revenue": "Annual_Revenue_Government_PropertyTax", "sales tax revenue": "Annual_Revenue_Government_SalesTax", "income tax revenue": "Annual_Revenue_Government_IncomeTax", "government revenue per capita": "Revenue_Government_PerCapita", "total government expenditure": "Annual_Expenditure_Government", "education spending": "Annual_Expenditure_Government_Education", "healthcare spending": "Annual_Expenditure_Government_Healthcare", "public safety spending": "Annual_Expenditure_Government_PublicSafety", "transportation spending": "Annual_Expenditure_Government_Transportation", "welfare spending": "Annual_Expenditure_Government_Welfare", "government spending per capita": "Expenditure_Government_PerCapita", "government debt": "Amount_Debt_Government", "government debt per capita": "Debt_Government_PerCapita", "debt-to-gdp ratio": "DebtToGDPRatio_Government", "registered voters": "Count_Person_RegisteredToVote", "voter registration rate": "VoterRegistrationRate_Person", "voter turnout": "VoterTurnout_Election", "presidential election votes": "Count_V<PERSON>_<PERSON>", "congressional election votes": "Count_Vote_Congressional", "total business establishments": "Count_Establishment", "small businesses": "Count_Establishment_SmallBusiness", "retail establishments": "Count_Establishment_Retail", "manufacturing establishments": "Count_Establishment_Manufacturing", "service establishments": "Count_Establishment_Services", "food service establishments": "Count_Establishment_FoodService", "business revenue": "Annual_Revenue_Business", "business payroll": "Annual_Payroll_Business", "business employees": "Count_Employee_Business", "business formation rate": "BusinessFormationRate", "business failure rate": "BusinessFailureRate", "retail sales": "Annual_Sales_Retail", "e-commerce sales": "Annual_Sales_ECommerce", "retail sales per capita": "Annual_Sales_Retail_PerCapita", "number of shopping centers": "Count_ShoppingCenter", "households with broadband": "Count_Household_BroadbandInternet", "households without internet": "Count_Household_NoInternetAccess", "broadband subscription rate": "BroadbandSubscriptionRate_Household", "average download speed": "Mean_InternetSpeed_Download", "average upload speed": "Mean_InternetSpeed_Upload", "households with computers": "Count_Household_Computer", "households with smartphones": "Count_Household_Smartphone", "households with tablets": "Count_Household_Tablet", "households without computers": "Count_Household_NoComputer", "mobile phone subscriptions": "Count_Person_MobilePhoneSubscription", "mobile subscription rate": "MobilePhoneSubscriptionRate_Person", "number of cell towers": "Count_CellTower", "people with disabilities": "Count_Person_WithDisability", "people with hearing difficulty": "Count_Person_WithHearingDifficulty", "people with vision difficulty": "Count_Person_WithVisionDifficulty", "people with cognitive difficulty": "Count_Person_WithCognitiveDifficulty", "people with ambulatory difficulty": "Count_Person_WithAmbulatoryDifficulty", "people with self-care difficulty": "Count_Person_WithSelfCareDifficulty", "people with independent living difficulty": "Count_Person_WithIndependentLivingDifficulty", "percentage with disabilities": "Percent_Person_WithDisability", "percentage with hearing difficulty": "Percent_Person_WithHearingDifficulty", "percentage with vision difficulty": "Percent_Person_WithVisionDifficulty", "percentage with cognitive difficulty": "Percent_Person_WithCognitiveDifficulty", "percentage with ambulatory difficulty": "Percent_Person_WithAmbulatoryDifficulty", "percentage with self-care difficulty": "Percent_Person_WithSelfCareDifficulty", "percentage with independent living difficulty": "Percent_Person_WithIndependentLivingDifficulty", "males with disabilities": "Count_Person_Male_WithDisability", "females with disabilities": "Count_Person_Female_WithDisability", "male disability rate": "Percent_Person_Male_WithDisability", "female disability rate": "Percent_Person_Female_WithDisability", "children with disabilities": "Count_Person_Under18Years_WithDisability", "child disability rate": "Percent_Person_Under18Years_WithDisability", "working-age adults with disabilities": "Count_Person_18To64Years_WithDisability", "working-age disability rate": "Percent_Person_18To64Years_WithDisability", "seniors with disabilities": "Count_Person_65OrMoreYears_WithDisability", "senior disability rate": "Percent_Person_65OrMoreYears_WithDisability", "white population with disabilities": "Count_Person_WhiteAlone_WithDisability", "white disability rate": "Percent_Person_WhiteAlone_WithDisability", "black population with disabilities": "Count_Person_BlackOrAfricanAmericanAlone_WithDisability", "black disability rate": "Percent_Person_BlackOrAfricanAmericanAlone_WithDisability", "asian population with disabilities": "Count_Person_AsianAlone_WithDisability", "asian disability rate": "Percent_Person_AsianAlone_WithDisability", "hispanic population with disabilities": "Count_Person_HispanicOrLatino_WithDisability", "hispanic disability rate": "Percent_Person_HispanicOrLatino_WithDisability", "employed people with disabilities": "Count_Person_WithDisability_Employed", "employment rate for people with disabilities": "Percent_Person_WithDisability_Employed", "unemployed people with disabilities": "Count_Person_WithDisability_Unemployed", "unemployment rate for people with disabilities": "Percent_Person_WithDisability_Unemployed", "people with disabilities not in labor force": "Count_Person_WithDisability_NotInLaborForce", "labor force non-participation rate for disabilities": "Percent_Person_WithDisability_NotInLaborForce", "people with disabilities with health insurance": "Count_Person_WithDisability_WithHealthInsurance", "health insurance rate for people with disabilities": "Percent_Person_WithDisability_WithHealthInsurance", "people with disabilities without health insurance": "Count_Person_WithDisability_NoHealthInsurance", "uninsured rate for people with disabilities": "Percent_Person_WithDisability_NoHealthInsurance", "people with multiple disabilities": "Count_Person_WithMultipleDisabilities", "multiple disability rate": "Percent_Person_WithMultipleDisabilities", "people with severe disabilities": "Count_Person_WithSevereDisability", "severe disability rate": "Percent_Person_WithSevereDisability", "people with work disability": "Count_Person_WithWorkDisability", "work disability rate": "Percent_Person_WithWorkDisability", "people receiving disability benefits": "Count_Person_ReceivingDisabilityBenefits", "disability benefit receipt rate": "Percent_Person_ReceivingDisabilityBenefits", "people receiving ssdi": "Count_Person_ReceivingSSDI", "ssdi receipt rate": "Percent_Person_ReceivingSSDI", "veteran population": "Count_Person_Veteran", "male veterans": "Count_Person_Veteran_Male", "female veterans": "Count_Person_Veteran_Female", "veterans with disability rating": "Count_Person_Veteran_DisabilityRating", "english-only speakers": "Count_Person_SpeakEnglishOnly", "spanish speakers": "Count_Person_SpeakSpanish", "asian language speakers": "Count_Person_SpeakAsianLanguage", "limited english proficiency": "Count_Person_LimitedEnglishProficiency", "public assistance recipients": "Count_Person_ReceivingPublicAssistance", "ssi recipients": "Count_Person_ReceivingSSI", "social security recipients": "Count_Person_ReceivingSocialSecurity", "unemployment benefit recipients": "Count_Person_ReceivingUnemploymentBenefits", "tanf recipients": "Count_Person_ReceivingTANF", "tanf receipt rate": "Percent_Person_ReceivingTANF", "families receiving tanf": "Count_Family_ReceivingTANF", "family tanf rate": "Percent_Family_ReceivingTANF", "welfare recipients": "Count_Person_Receiving<PERSON><PERSON><PERSON>", "welfare receipt rate": "Percent_Person_Receiving<PERSON><PERSON>are", "food stamp recipients": "Count_Person_ReceivingFoodStamps", "food stamp receipt rate": "Percent_Person_ReceivingFoodStamps", "housing assistance recipients": "Count_Person_ReceivingHousingAssistance", "housing assistance rate": "Percent_Person_ReceivingHousingAssistance", "in public housing": "Count_Person_PublicHousing", "public housing rate": "Percent_Person_PublicHousing", "section 8 recipients": "Count_Person_Section8", "section 8 rate": "Percent_Person_Section8", "childcare assistance recipients": "Count_Person_ReceivingChildcare", "childcare assistance rate": "Percent_Person_ReceivingChildcare", "eitc recipients": "Count_Person_ReceivingEITC", "eitc receipt rate": "Percent_Person_ReceivingEITC", "child tax credit recipients": "Count_Person_ReceivingChildTaxCredit", "child tax credit rate": "Percent_Person_ReceivingChildTaxCredit", "means-tested benefit recipients": "Count_Person_ReceivingMeansTestedBenefits", "means-tested benefit rate": "Percent_Person_ReceivingMeansTestedBenefits", "multiple benefit recipients": "Count_Person_ReceivingMultipleBenefits", "multiple benefit rate": "Percent_Person_ReceivingMultipleBenefits", "at benefit cliff": "Count_Person_BenefitCliff", "benefit cliff rate": "Percent_Person_BenefitCliff", "eligible non-recipients": "Count_Person_EligibleButNotReceivingBenefits", "benefit non-takeup rate": "Percent_Person_EligibleButNotReceivingBenefits", "snap takeup rate": "BenefitTakeupRate_SNAP", "tanf takeup rate": "BenefitTakeupRate_TANF", "medicaid takeup rate": "BenefitTakeupRate_Medicaid", "wic takeup rate": "BenefitTakeupRate_WIC", "eitc takeup rate": "BenefitTakeupRate_EITC", "energy assistance recipients": "Count_Person_ReceivingEnergyAssistance", "energy assistance rate": "Percent_Person_ReceivingEnergyAssistance", "utility assistance recipients": "Count_Person_ReceivingUtilityAssistance", "utility assistance rate": "Percent_Person_ReceivingUtilityAssistance", "free/reduced lunch recipients": "Count_Person_FreeReducedLunch", "free/reduced lunch rate": "Percent_Person_FreeReducedLunch", "school breakfast recipients": "Count_Person_SchoolBreakfast", "school breakfast rate": "Percent_Person_SchoolBreakfast", "summer food program recipients": "Count_Person_SummerFoodProgram", "summer food program rate": "Percent_Person_SummerFoodProgram", "head start participants": "Count_Person_HeadStart", "head start participation rate": "Percent_Person_HeadStart", "veterans benefit recipients": "Count_Person_ReceivingVeteransBenefits", "veterans benefit rate": "Percent_Person_ReceivingVeteransBenefits", "export value": "Annual_Value_Export", "import value": "Annual_Value_Import", "trade balance": "TradeBalance", "goods exports": "Annual_Value_Export_Goods", "services exports": "Annual_Value_Export_Services", "goods imports": "Annual_Value_Import_Goods", "services imports": "Annual_Value_Import_Services", "inbound fdi": "Amount_ForeignDirectInvestment_Inbound", "outbound fdi": "Amount_ForeignDirectInvestment_Outbound", "fdi stock": "Stock_ForeignDirectInvestment", "number of banks": "Count_Bank", "number of bank branches": "Count_BankBranch", "bank deposits": "Amount_Deposit_Bank", "bank loans": "Amount_Loan_Bank", "unbanked population": "Count_Person_Unbanked", "average credit score": "Mean_CreditScore", "median credit card debt": "Median_CreditCardDebt", "number of bankruptcies": "Count_Bankruptcy", "bankruptcy rate": "BankruptcyRate", "number of foreclosures": "Count_Foreclosure", "stock market index value": "StockMarketIndex", "market capitalization": "MarketCapitalization", "trading volume": "TradingVolume", "access to safe water": "Count_Person_WithAccessToSafeWater", "without safe water access": "Count_Person_WithoutAccessToSafeWater", "water access rate": "WaterAccessRate_Person", "water consumption": "Annual_Consumption_Water", "water consumption per capita": "Annual_Consumption_Water_PerCapita", "access to sanitation": "Count_Person_WithAccessToSanitation", "without sanitation access": "Count_Person_WithoutAccessToSanitation", "sanitation access rate": "SanitationAccessRate_Person", "wastewater treatment plants": "Count_WastewaterTreatmentPlant", "tourist arrivals": "Count_TouristArrival", "tourism revenue": "Annual_Revenue_Tourism", "number of hotels": "Count_Hotel", "number of hotel rooms": "Count_HotelRoom", "hotel occupancy rate": "HotelOccupancyRate", "number of parks": "Count_<PERSON>", "park area": "Area_Park", "recreation facilities": "Count_RecreationFacility", "number of museums": "Count_Museum", "number of theaters": "Count_Theater", "children in foster care": "Count_Person_InFosterCare", "adopted children": "Count_Person_Adopted", "child abuse rate": "ChildAbuseRate", "youth in detention": "Count_Person_Ju<PERSON>ile<PERSON><PERSON><PERSON><PERSON>", "nursing home residents": "Count_Person_InNursingHome", "home care recipients": "Count_Person_ReceivingHomeCare", "seniors living alone": "Count_Person_65<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s_LivingAlone", "homeless population": "Count_Person_Homeless", "sheltered homeless": "Count_Person_Homeless_Sheltered", "unsheltered homeless": "Count_Person_Homeless_Unsheltered", "homelessness rate": "HomelessnessRate_Person", "chronically homeless population": "Count_Person_ChronicallyHomeless", "chronic homelessness rate": "Percent_Person_ChronicallyHomeless", "homeless veterans": "Count_Person_Homeless_Veteran", "veteran homelessness rate": "Percent_Person_Homeless_Veteran", "homeless families": "Count_Person_Homeless_Family", "homeless youth": "Count_Person_Homeless_Youth", "unaccompanied homeless youth": "Count_Person_Homeless_Unaccompanied<PERSON>outh", "youth homelessness rate": "Percent_Person_Homeless_Youth", "at risk of homelessness": "Count_Person_AtRiskOfHomelessness", "experiencing housing instability": "Count_Person_ExperiencingHousingInstability", "doubled-up households": "Count_Person_Doubled_Up", "evicted population": "Count_Person_Evicted", "eviction rate": "EvictionRate", "receiving homeless services": "Count_Person_ReceivingHomelessServices", "in transitional housing": "Count_Person_TransitionalHousing", "in permanent supportive housing": "Count_Person_PermanentSupportiveHousing", "in rapid rehousing programs": "Count_Person_RapidRehousing", "median income as percent of poverty level": "MedianIncomeAsPctOfPovertyLevel", "extreme poverty (below 30% poverty line)": "Count_Person_Below30PercentPovertyLevel", "extreme poverty rate": "Percent_Person_Below30PercentPovertyLevel", "100-199% of poverty level": "Count_Person_100To199PercentPovertyLevel", "100-199% poverty level rate": "Percent_Person_100To199PercentPovertyLevel", "200-299% of poverty level": "Count_Person_200To299PercentPovertyLevel", "200-299% poverty level rate": "Percent_Person_200To299PercentPovertyLevel", "300-399% of poverty level": "Count_Person_300To399PercentPovertyLevel", "300-399% poverty level rate": "Percent_Person_300To399PercentPovertyLevel", "400%+ of poverty level": "Count_Person_400PercentOrMorePovertyLevel", "400%+ poverty level rate": "Percent_Person_400PercentOrMorePovertyLevel", "alice population (asset limited income constrained employed)": "Count_Person_ALICE", "alice rate": "Percent_Person_ALICE", "earning living wage": "Count_Person_LivingWage", "living wage rate": "Percent_Person_LivingWage", "earning minimum wage": "Count_Person_MinimumWage", "minimum wage rate": "Percent_Person_MinimumWage", "earning subminimum wage": "Count_Person_SubminimumWage", "subminimum wage rate": "Percent_Person_SubminimumWage", "poverty gap index": "PovertyGap", "poverty gap squared (severity)": "PovertyGapSquared", "in persistent poverty": "Count_Person_PersistentPoverty", "persistent poverty rate": "Percent_Person_PersistentPoverty", "in generational poverty": "Count_Person_GenerationalPoverty", "generational poverty rate": "Percent_Person_GenerationalPoverty", "working poor rate": "Percent_Person_WorkingPoor", "asset poor": "Count_Person_<PERSON>", "asset poverty rate": "Percent_<PERSON>_AssetPoor", "liquid asset poor": "Count_Person_LiquidAssetPoor", "liquid asset poverty rate": "Percent_Person_LiquidAssetPoor"}, "place_types": {"county": "County", "state": "State", "city": "City", "country": "Country", "province": "Province", "district": "District", "zip": "Zip", "census tract": "CensusTract", "school district": "SchoolDistrict", "congressional district": "CongressionalDistrict", "state legislative district": "StateLegislativeDistrict"}}