# NLWeb Release Notes

Updates from July 9 - July 29, 2025

### Major Features & Enhancements

- **Ollama Support Added** (#184) - Added comprehensive support for Ollama as an LLM provider
- **Clinical Trials Demo** (#247) - New demonstration showcasing clinical trials data upload with comprehensive documentation

### Codebase Cleanup

- Removed legacy old_code and old_static directories (#269)
- Code refactoring improvements and critical path fixes (#267)

### Documentation Updates

- Updated README with PostgreSQL information (#294)
- General README improvements and clarifications (#278)

### Bug Fixes & Improvements

- Added requirement for item type specification in URL parameters (#297)
- Fixed state management naming issues for ToolSelector step completion (#296)
- Resolved post-refactoring issues with Docker-based installations (#295)
- Fixed Gemini embedding bug (#291)
- Fixed MCP test interface to support both standard and stream requests (#285)
- Fixed issue where NLWS message types were not displayed properly (#283)
- Fixed Elasticsearch post-refactoring (#281)
- Improved get_param function to properly handle list parameters (#273)
- Added CodeQL Fork Analysis Workflow for automated security scanning (#241)

---
*Total PRs merged: 15*  
*Contributors: 11 developers*