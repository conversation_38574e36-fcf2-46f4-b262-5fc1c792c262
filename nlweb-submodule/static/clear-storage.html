<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Local Storage</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 30px;
        }
        .site-section {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .site-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f5f5f5;
            cursor: pointer;
            user-select: none;
        }
        .site-header:hover {
            background: #ebebeb;
        }
        .site-info {
            flex: 1;
        }
        .site-name {
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .site-stats {
            font-size: 14px;
            color: #666;
        }
        .expand-icon {
            transition: transform 0.2s;
            display: inline-block;
        }
        .expanded .expand-icon {
            transform: rotate(90deg);
        }
        .conversations-list {
            display: none;
            padding: 0;
            background: white;
            max-height: 400px;
            overflow-y: auto;
        }
        .expanded .conversations-list {
            display: block;
        }
        .conversation-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        .conversation-item:last-child {
            border-bottom: none;
        }
        .conversation-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        .conversation-meta {
            color: #666;
            font-size: 13px;
        }
        .conversation-preview {
            color: #888;
            font-size: 13px;
            margin-top: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #dc3545;
            background: white;
            color: #dc3545;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #dc3545;
            color: white;
        }
        .clear-all-btn {
            width: 100%;
            padding: 12px;
            margin-top: 30px;
            font-size: 16px;
        }
        .back-link {
            color: #0066cc;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <a href="multi-chat-index.html" class="back-link">← Back to Chat</a>
    
    <h1>Clear Conversations by Site</h1>
    
    <div id="sitesList"></div>
    
    <button class="clear-all-btn" onclick="clearAll()">Clear All Conversations</button>

    <script>
        function formatDate(timestamp) {
            if (!timestamp) return 'Unknown date';
            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);
            
            if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
            if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
            if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
            
            return date.toLocaleDateString();
        }
        
        function toggleSection(siteId) {
            const section = document.getElementById(`section-${siteId}`);
            section.classList.toggle('expanded');
        }
        
        function loadSites() {
            const data = localStorage.getItem('nlweb_messages');
            const container = document.getElementById('sitesList');
            
            if (!data) {
                container.innerHTML = '<div class="empty-state">No conversations stored</div>';
                return;
            }
            
            try {
                const messages = JSON.parse(data);
                
                // Group messages by conversation_id
                const conversationMap = {};
                messages.forEach(msg => {
                    const convId = msg.conversation_id;
                    if (!convId) return;
                    
                    if (!conversationMap[convId]) {
                        conversationMap[convId] = {
                            id: convId,
                            messages: [],
                            site: msg.site || 'all',
                            timestamp: msg.timestamp
                        };
                    }
                    
                    conversationMap[convId].messages.push(msg);
                    
                    // Update site if we find it in a message
                    if (msg.site && conversationMap[convId].site === 'all') {
                        conversationMap[convId].site = msg.site;
                    }
                    
                    // Update timestamp to be the latest
                    if (msg.timestamp > conversationMap[convId].timestamp) {
                        conversationMap[convId].timestamp = msg.timestamp;
                    }
                });
                
                // Group conversations by site
                const siteGroups = {};
                Object.values(conversationMap).forEach(conv => {
                    const site = conv.site || 'all';
                    if (!siteGroups[site]) {
                        siteGroups[site] = [];
                    }
                    siteGroups[site].push(conv);
                });
                
                // Display sites
                container.innerHTML = '';
                Object.keys(siteGroups).sort().forEach(site => {
                    const convs = siteGroups[site];
                    const totalMessages = convs.reduce((sum, c) => sum + c.messages.length, 0);
                    const siteId = site.replace(/[^a-zA-Z0-9]/g, '_');
                    
                    // Sort conversations by timestamp (newest first)
                    convs.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
                    
                    const siteSection = document.createElement('div');
                    siteSection.className = 'site-section';
                    siteSection.id = `section-${siteId}`;
                    
                    const conversationsList = convs.map(conv => {
                        // Get first user message for title
                        const firstUserMsg = conv.messages.find(m => m.message_type === 'user');
                        const title = firstUserMsg ? 
                            (typeof firstUserMsg.content === 'string' ? 
                                firstUserMsg.content : firstUserMsg.content?.content || 'Untitled') 
                            : 'Untitled';
                        const shortTitle = title.substring(0, 100) + (title.length > 100 ? '...' : '');
                        
                        // Get last message for preview
                        const lastMsg = conv.messages[conv.messages.length - 1];
                        let preview = '';
                        if (lastMsg && lastMsg.message_type === 'user') {
                            const content = typeof lastMsg.content === 'string' ? 
                                lastMsg.content : lastMsg.content?.content || '';
                            preview = content.substring(0, 150) + (content.length > 150 ? '...' : '');
                        }
                        
                        return `
                            <div class="conversation-item">
                                <div class="conversation-title">${shortTitle}</div>
                                <div class="conversation-meta">
                                    ${formatDate(conv.timestamp)} • ${conv.messages.length} messages
                                </div>
                                ${preview ? `<div class="conversation-preview">${preview}</div>` : ''}
                            </div>
                        `;
                    }).join('');
                    
                    siteSection.innerHTML = `
                        <div class="site-header" onclick="toggleSection('${siteId}')">
                            <div class="site-info">
                                <div class="site-name">
                                    <span class="expand-icon">▶</span>
                                    ${site}
                                </div>
                                <div class="site-stats">${convs.length} conversation${convs.length !== 1 ? 's' : ''}, ${totalMessages} message${totalMessages !== 1 ? 's' : ''}</div>
                            </div>
                            <button onclick="event.stopPropagation(); clearSite('${site}')">Clear</button>
                        </div>
                        <div class="conversations-list">
                            ${conversationsList}
                        </div>
                    `;
                    container.appendChild(siteSection);
                });
                
            } catch (e) {
                console.error('Error loading conversations:', e);
                container.innerHTML = '<div class="empty-state">Error loading conversations</div>';
            }
        }
        
        function clearSite(site) {
            if (!confirm(`Clear all conversations for "${site}"?`)) return;
            
            const data = localStorage.getItem('nlweb_messages');
            if (!data) return;
            
            try {
                const messages = JSON.parse(data);
                
                // Find all conversation IDs for this site
                const conversationsToDelete = new Set();
                messages.forEach(msg => {
                    if ((msg.site || 'all') === site) {
                        conversationsToDelete.add(msg.conversation_id);
                    }
                });
                
                // Filter out messages from those conversations
                const filtered = messages.filter(msg => !conversationsToDelete.has(msg.conversation_id));
                localStorage.setItem('nlweb_messages', JSON.stringify(filtered));
                loadSites();
            } catch (e) {
                alert('Error clearing site conversations');
            }
        }
        
        function clearAll() {
            if (!confirm('Clear ALL conversations?')) return;
            if (!confirm('This cannot be undone. Are you sure?')) return;
            
            localStorage.removeItem('nlweb_messages');
            loadSites();
        }
        
        // Load on startup
        loadSites();
    </script>
</body>
</html>