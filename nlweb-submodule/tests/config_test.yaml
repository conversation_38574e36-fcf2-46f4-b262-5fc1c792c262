# Test-specific configuration for multi-participant chat system
# This configuration is used for running integration and unit tests

server:
  host: "localhost"
  port: 8080
  enable_cors: true
  max_connections: 50  # Lower for tests

# Mode configuration
mode: "test"

chat:
  storage:
    backend: "memory"  # Always use memory backend for tests
    queue_size_limit: 100  # Lower limit for tests
    cache_enabled: false  # Disable caching for predictable tests
  
  # Timeouts optimized for fast test execution
  single_mode_timeout: 2  # 2 seconds for tests
  multi_mode_timeout: 5   # 5 seconds for tests
  
  # Lower limits for testing edge cases
  limits:
    max_participants: 10
    queue_size: 100
  
  # Faster timeouts for test responsiveness
  context:
    nlweb_timeout: 2  # 2 seconds for tests

# Authentication disabled for tests
auth:
  enabled: false
  mock_user:
    user_id: "test_user_123"
    name: "Test User"
    email: "<EMAIL>"

# NLWeb integration (mocked in tests)
nlweb:
  enabled: false  # Disabled for unit tests
  mock_responses: true
  timeout: 2

# WebSocket configuration for tests
websocket:
  ping_interval: 10  # Faster pings for tests
  ping_timeout: 5
  max_message_size: 1048576  # 1MB

# Logging configuration for tests
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  disable_access_logs: true  # Cleaner test output

# Test-specific settings
test:
  # Reset state between tests
  auto_cleanup: true
  
  # Test data settings
  fixtures:
    sample_users: 5
    sample_conversations: 3
    sample_messages_per_conversation: 10
  
  # Performance test thresholds
  performance:
    max_latency_ms: 50
    max_memory_mb: 100
    
  # Retry settings for flaky tests
  retry:
    max_attempts: 3
    delay_ms: 100