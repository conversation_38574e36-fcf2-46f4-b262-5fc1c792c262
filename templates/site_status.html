<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ site_name }} - Crawl Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navigation {
            margin-bottom: 20px;
        }
        .navigation a {
            margin-right: 15px;
            text-decoration: none;
            color: #4CAF50;
        }
        .navigation a:hover {
            text-decoration: underline;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .status-item {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
        }
        .status-item h3 {
            margin: 0 0 10px 0;
            color: #555;
            font-size: 14px;
            text-transform: uppercase;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .action-buttons {
            margin: 20px 0;
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn-pause {
            background-color: #ff9800;
            color: white;
        }
        .btn-pause:hover:not(:disabled) {
            background-color: #e68900;
        }
        .btn-resume {
            background-color: #4CAF50;
            color: white;
        }
        .btn-resume:hover:not(:disabled) {
            background-color: #45a049;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .btn-refresh {
            background-color: #2196F3;
            color: white;
        }
        .btn-refresh:hover {
            background-color: #1976D2;
        }
        .btn-reset {
            background-color: #9c27b0;
            color: white;
        }
        .btn-reset:hover {
            background-color: #7b1fa2;
        }
        .btn-delete {
            background-color: #f44336;
            color: white;
        }
        .btn-delete:hover {
            background-color: #d32f2f;
        }
        .last-updated {
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
        .status-paused {
            color: #ff9800;
        }
        .status-running {
            color: #4CAF50;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #2196F3;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .json-section {
            margin-top: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
        }
        .json-section h2 {
            margin-top: 0;
            color: #333;
            font-size: 20px;
        }
        .json-object {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
        }
        .json-object:last-child {
            margin-bottom: 0;
        }
        .json-timestamp {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .json-url {
            color: #007bff;
            font-size: 12px;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .json-content {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
        .json-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 12px;
            color: #007bff;
            cursor: pointer;
            text-decoration: none;
        }
        .json-toggle:hover {
            text-decoration: underline;
        }
        .json-content.collapsed {
            max-height: 60px;
            overflow: hidden;
        }
        .no-json {
            color: #6c757d;
            font-style: italic;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 100px;
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .copy-button:hover {
            background-color: #0056b3;
        }
        .copy-button.copied {
            background-color: #28a745;
        }
        .copy-icon {
            width: 14px;
            height: 14px;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="/">Summary</a>
        <a href="/status">All Sites</a>
        <a href="/add">Add Site</a>
        <a href="/log">Log</a>
        <a href="/error_log">Error Log</a>
    </div>
    
    <div class="container">
        <a href="/status" class="back-link">← Back to all sites</a>
        
        <h1>{{ site_name }}</h1>
        
        {% if status.processing %}
        <div class="status-item" style="background-color: #fff3cd; border-left-color: #ffc107; margin-bottom: 20px;">
            <h3 style="color: #856404;">Processing Sitemaps</h3>
            <div style="color: #856404;">This site is currently being processed. URL collection is in progress...</div>
        </div>
        {% elif not status.sitemap_processed %}
        <div class="status-item" style="background-color: #e3f2fd; border-left-color: #2196F3; margin-bottom: 20px;">
            <h3 style="color: #0d47a1;">Waiting for Sitemap Processing</h3>
            <div style="color: #0d47a1;">Sitemap processing needs to complete before crawling can begin.</div>
        </div>
        {% endif %}
        
        <div class="status-grid">
            <div class="status-item">
                <h3>Total URLs</h3>
                <div class="status-value">{{ status.total_urls }}</div>
            </div>
            <div class="status-item">
                <h3>Crawled URLs</h3>
                <div class="status-value">{{ status.crawled_urls }}</div>
            </div>
            <div class="status-item">
                <h3>Status</h3>
                <div class="status-value {% if status.paused %}status-paused{% else %}status-running{% endif %}">
                    {% if status.processing %}
                        Processing Sitemaps
                    {% elif not status.sitemap_processed %}
                        Waiting
                    {% elif status.paused %}
                        Paused
                    {% else %}
                        Running
                    {% endif %}
                </div>
            </div>
            <div class="status-item">
                <h3>Completion</h3>
                <div class="status-value">
                    {% if status.total_urls > 0 %}
                        {{ ((status.crawled_urls / status.total_urls) * 100) | round(1) }}%
                    {% else %}
                        0%
                    {% endif %}
                </div>
            </div>
            <div class="status-item">
                <h3>JSON Objects</h3>
                <div class="status-value">{{ json_stats.total_objects }}</div>
            </div>
            <div class="status-item">
                <h3>Object Types</h3>
                <div class="status-value" style="font-size: 14px;">
                    {% if json_stats.type_counts %}
                        {% for type_name, count in json_stats.type_counts.items() %}
                            <div>{{ type_name }}: {{ count }}</div>
                        {% endfor %}
                    {% else %}
                        <div style="color: #999;">None found</div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        {% if status.errors %}
        <div class="status-item" style="margin-top: 20px; background-color: #ffebee;">
            <h3>Errors</h3>
            <div style="font-size: 16px;">
                {% for error_code, count in status.errors.items() %}
                    <div style="margin: 5px 0;">
                        <strong>{{ error_code }}:</strong> {{ count }} 
                        {% if error_code == '429' %}(Rate Limited){% endif %}
                        {% if error_code == 'TIMEOUT' %}(Timeouts){% endif %}
                        {% if error_code == 'ERROR' %}(Generic Errors){% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        
        <div class="action-buttons">
            <button id="pauseBtn" 
                    class="btn btn-pause"
                    onclick="pauseCrawl()"
                    {% if status.paused %}disabled{% endif %}>
                Pause
            </button>
            <button id="resumeBtn" 
                    class="btn btn-resume"
                    onclick="resumeCrawl()"
                    {% if not status.paused %}disabled{% endif %}>
                Resume
            </button>
            <button class="btn btn-refresh" onclick="location.reload()">
                Refresh Status
            </button>
            <button class="btn btn-delete" onclick="deleteSite()">
                Delete Site
            </button>
        </div>
        
        <div class="last-updated">
            Last updated: {{ status.last_updated }}
        </div>
        
        <div class="json-section">
            <h2>Recent Schema.org Data (Last 5)</h2>
            {% if json_objects %}
                {% for obj in json_objects %}
                    <div class="json-object">
                        <button class="copy-button" onclick="copyToClipboard('json-{{ loop.index }}', this)">
                            <svg class="copy-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                            </svg>
                            <span>Copy</span>
                        </button>
                        <a href="#" class="json-toggle" onclick="toggleJson(event, 'json-{{ loop.index }}')">Expand/Collapse</a>
                        {% if obj.timestamp %}
                            <div class="json-timestamp">Captured: {{ obj.timestamp }}</div>
                        {% endif %}
                        {% if obj.url %}
                            <div class="json-url">From: {{ obj.url }}</div>
                        {% endif %}
                        <div id="json-{{ loop.index }}" class="json-content collapsed">
                            <pre>{{ obj.data | tojson(indent=2) }}</pre>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <p class="no-json">No schema.org data collected yet.</p>
            {% endif %}
        </div>
    </div>
    
    <script>
        const siteName = {{ site_name | tojson }};
        const status = {{ status | tojson }};
        const json_stats = {{ json_stats | tojson }};
        
        function toggleJson(event, elementId) {
            event.preventDefault();
            const element = document.getElementById(elementId);
            element.classList.toggle('collapsed');
        }
        
        function copyToClipboard(elementId, button) {
            const element = document.getElementById(elementId);
            const textToCopy = element.querySelector('pre').textContent;
            
            // Use the modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // Show success feedback
                    button.classList.add('copied');
                    const originalText = button.querySelector('span').textContent;
                    button.querySelector('span').textContent = 'Copied!';
                    
                    // Reset after 2 seconds
                    setTimeout(() => {
                        button.classList.remove('copied');
                        button.querySelector('span').textContent = originalText;
                    }, 2000);
                }).catch(err => {
                    console.error('Failed to copy:', err);
                    fallbackCopyTextToClipboard(textToCopy, button);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(textToCopy, button);
            }
        }
        
        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            
            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    // Show success feedback
                    button.classList.add('copied');
                    const originalText = button.querySelector('span').textContent;
                    button.querySelector('span').textContent = 'Copied!';
                    
                    // Reset after 2 seconds
                    setTimeout(() => {
                        button.classList.remove('copied');
                        button.querySelector('span').textContent = originalText;
                    }, 2000);
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
            }
            
            document.body.removeChild(textArea);
        }
        
        async function pauseCrawl() {
            try {
                const response = await fetch(`/toggle_pause/${siteName}`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Error pausing crawl');
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        }
        
        async function resumeCrawl() {
            try {
                const response = await fetch(`/toggle_pause/${siteName}`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Error resuming crawl');
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        }
        
        async function resetCrawl() {
            if (!confirm(`Are you sure you want to RESET and restart ${siteName}?\n\nThis will:\n• Delete ALL existing data (${status.crawled_urls} crawled pages)\n• Delete ALL JSON data (${json_stats.total_objects} objects)\n• Refetch the sitemap from scratch\n• Start over completely\n\nThis is different from Pause/Resume which keeps your progress.`)) {
                return;
            }
            
            try {
                const response = await fetch(`/restart_crawl/${siteName}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    alert(data.message);
                    // Reload the page to show the new status
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    alert(`Error restarting crawl: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                alert(`Error restarting crawl: ${error.message}`);
            }
        }
        
        async function deleteSite() {
            if (!confirm(`Are you sure you want to DELETE ${siteName}?\n\nThis will PERMANENTLY delete:\n• All URLs\n• All crawled documents\n• All JSON data\n• All status information\n\nThis action cannot be undone!`)) {
                return;
            }
            
            // Double confirmation for safety
            if (!confirm(`This is your final warning!\n\nAre you ABSOLUTELY SURE you want to delete ${siteName}?\n\nAll data will be permanently lost.`)) {
                return;
            }
            
            try {
                const response = await fetch(`/delete_site/${siteName}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    alert(data.message);
                    // Redirect to status page after deletion
                    window.location.href = '/status';
                } else {
                    alert(`Error deleting site: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                alert(`Error deleting site: ${error.message}`);
            }
        }
        
        
        // Auto-refresh every 5 seconds
        setInterval(() => {
            location.reload();
        }, 5000);
    </script>
</body>
</html>