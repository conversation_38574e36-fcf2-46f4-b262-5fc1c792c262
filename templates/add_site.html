<!DOCTYPE html>
<html>
<head>
    <title>Web Crawler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            font-family: Arial, sans-serif;
            resize: vertical;
        }
        .mode-button {
            background-color: #e0e0e0;
            color: #333;
            border: none;
            padding: 8px 16px;
            margin-right: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .mode-button.active {
            background-color: #4CAF50;
            color: white;
        }
        .mode-button:hover {
            opacity: 0.8;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .navigation {
            margin-bottom: 20px;
        }
        .navigation a {
            margin-right: 15px;
            text-decoration: none;
            color: #4CAF50;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 4px;
            display: none;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="/">Summary</a>
        <a href="/status">Status</a>
        <a href="/add">Add Site</a>
        <a href="/log">Log</a>
        <a href="/error_log">Error Log</a>
    </div>
    
    <h1>Add New Site</h1>
    
    <div style="margin-bottom: 20px;">
        <button type="button" id="singleModeBtn" class="mode-button active" onclick="switchMode('single')">Single Site</button>
        <button type="button" id="multiModeBtn" class="mode-button" onclick="switchMode('multiple')">Multiple Sites</button>
    </div>
    
    <form id="crawlForm">
        <div id="singleMode">
            <div class="form-group">
                <label for="siteName">Site Name (optional):</label>
                <input type="text" id="siteName" name="siteName" placeholder="e.g., my_recipe_site">
                <small style="color: #666;">Leave blank to use domain name. Use only letters, numbers, and underscores.</small>
            </div>
            
            <div class="form-group">
                <label for="url">Website or Sitemap URL:</label>
                <input type="text" id="url" name="url" placeholder="https://example.com or https://example.com/sitemap.xml">
            </div>
            
            <div class="form-group">
                <label for="filter">URL Filter (optional):</label>
                <input type="text" id="filter" name="filter" placeholder="Enter text to filter URLs">
            </div>
        </div>
        
        <div id="multipleMode" style="display: none;">
            <div class="form-group">
                <label for="urls">Websites or Sitemap URLs (one per line):</label>
                <textarea id="urls" name="urls" rows="10" placeholder="https://example1.com&#10;https://example2.com/sitemap.xml&#10;https://example3.com"></textarea>
            </div>
            <p style="font-size: 14px; color: #666;">Enter one URL per line. The filter option is not available for multiple sites.</p>
        </div>
        
        <button type="submit">Process</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        let currentMode = 'single';
        
        async function pollProcessingStatus(siteName, elementId) {
            const statusDiv = document.getElementById(elementId);
            if (!statusDiv) return;
            
            try {
                const response = await fetch(`/processing_status/${siteName}`);
                const status = await response.json();
                
                if (status.status === 'processing') {
                    statusDiv.innerHTML = `<em>${status.message || 'Processing...'}</em>`;
                    // Continue polling
                    setTimeout(() => pollProcessingStatus(siteName, elementId), 2000);
                } else if (status.status === 'completed') {
                    statusDiv.innerHTML = `<strong style="color: green;">✓ Processing complete!</strong>`;
                    if (status.urls_found !== undefined) {
                        statusDiv.innerHTML += ` Found ${status.urls_found} URLs.`;
                    }
                } else if (status.status === 'error') {
                    statusDiv.innerHTML = `<strong style="color: red;">✗ Error: ${status.message}</strong>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<strong style="color: red;">✗ Error checking status</strong>`;
            }
        }
        
        function switchMode(mode) {
            currentMode = mode;
            
            document.getElementById('singleMode').style.display = mode === 'single' ? 'block' : 'none';
            document.getElementById('multipleMode').style.display = mode === 'multiple' ? 'block' : 'none';
            
            document.getElementById('singleModeBtn').classList.toggle('active', mode === 'single');
            document.getElementById('multiModeBtn').classList.toggle('active', mode === 'multiple');
            
            // Clear result when switching modes
            document.getElementById('result').style.display = 'none';
        }
        
        document.getElementById('crawlForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            
            if (currentMode === 'single') {
                // Single site mode
                const url = document.getElementById('url').value;
                const filter = document.getElementById('filter').value;
                const siteName = document.getElementById('siteName').value.trim();
                
                if (!url.trim()) {
                    resultDiv.innerHTML = '<div class="error">Please enter a URL</div>';
                    return;
                }
                
                // Validate site name if provided
                if (siteName && !/^[a-zA-Z0-9_]+$/.test(siteName)) {
                    resultDiv.innerHTML = '<div class="error">Site name can only contain letters, numbers, and underscores</div>';
                    return;
                }
                
                resultDiv.innerHTML = 'Processing...';
                
                try {
                    const response = await fetch('/process', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url, filter, site_name: siteName })
                    });
                
                const data = await response.json();
                
                if (response.ok) {
                    if (data.already_existed) {
                        // Site already existed, show as success anyway
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>Site Already Exists</h3>
                                <p>Site: ${data.site_name}</p>
                                <p>This site is already being crawled with ${data.total_urls} URLs.</p>
                                <p>
                                    <a href="/status/${data.site_name}">View site status</a> | 
                                    <a href="/status">View all sites</a>
                                </p>
                            </div>
                        `;
                    } else if (data.processing) {
                        // New site being processed
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>Processing Started</h3>
                                <p>Site: ${data.site_name}</p>
                                <p>Processing sitemaps in the background...</p>
                                <div id="processing-status">Checking status...</div>
                                <p>
                                    <a href="/status/${data.site_name}">View site status</a> | 
                                    <a href="/status">View all sites</a>
                                </p>
                            </div>
                        `;
                        // Start polling for status
                        pollProcessingStatus(data.site_name, 'processing-status');
                    } else {
                        // New site added (old response format)
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>Success!</h3>
                                <p>Site: ${data.site_name}</p>
                                <p>URLs found in this batch: ${data.urls_found}</p>
                                <p>Total URLs for site: ${data.total_urls}</p>
                                <p>
                                    <a href="/status/${data.site_name}">View site status</a> | 
                                    <a href="/status">View all sites</a>
                                </p>
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                }
            } else {
                // Multiple sites mode
                const urls = document.getElementById('urls').value;
                
                if (!urls.trim()) {
                    resultDiv.innerHTML = '<div class="error">Please enter at least one URL</div>';
                    return;
                }
                
                // Split URLs by newline and filter out empty lines
                const urlList = urls.split('\n').filter(u => u.trim());
                
                if (urlList.length === 0) {
                    resultDiv.innerHTML = '<div class="error">Please enter at least one valid URL</div>';
                    return;
                }
                
                resultDiv.innerHTML = `Processing ${urlList.length} sites...`;
                
                try {
                    const response = await fetch('/process_multiple', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ urls: urlList })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        let html = '<div class="success"><h3>Processing Started</h3>';
                        html += `<p>${data.message}</p>`;
                        html += `<p>Processing ${data.total_sites} sites:</p><ul>`;
                        
                        data.results.forEach((result, index) => {
                            html += `<li><strong>${result.site_name}</strong>: `;
                            if (result.already_existed) {
                                html += `Already exists with ${result.total_urls} URLs`;
                            } else if (result.processing) {
                                html += `<span id="multi-status-${index}">Processing...</span>`;
                            }
                            html += ` [<a href="/status/${result.site_name}">View</a>]</li>`;
                        });
                        
                        html += '</ul><p><a href="/status">View all sites</a></p></div>';
                        resultDiv.innerHTML = html;
                        
                        // Start polling for sites that are processing
                        data.results.forEach((result, index) => {
                            if (result.processing && !result.already_existed) {
                                pollProcessingStatus(result.site_name, `multi-status-${index}`);
                            }
                        });
                    } else {
                        resultDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                }
            }
        });
    </script>
</body>
</html>