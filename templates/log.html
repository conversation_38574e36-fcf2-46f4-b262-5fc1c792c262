<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawler Log</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .log-container {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 600px;
            overflow-y: auto;
        }
        .log-line {
            white-space: nowrap;
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-line:hover {
            background-color: #2a2a2a;
        }
        .timestamp {
            color: #569cd6;
            margin-right: 10px;
        }
        .url {
            color: #ce9178;
            margin-right: 10px;
        }
        .status-200 {
            color: #4ec9b0;
        }
        .status-error {
            color: #f44747;
        }
        .status-other {
            color: #dcdcaa;
        }
        .content-length {
            color: #b5cea8;
        }
        .refresh-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-button:hover {
            background-color: #45a049;
        }
        .info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 10px;
            margin-bottom: 20px;
        }
        .nav-links {
            margin-bottom: 20px;
        }
        .nav-links a {
            margin-right: 15px;
            color: #4CAF50;
            text-decoration: none;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Crawler Log</h1>
        
        <div class="nav-links">
            <a href="/">Summary</a>
            <a href="/status">Status</a>
            <a href="/add">Add Site</a>
            <a href="/log">Log</a>
            <a href="/error_log">Error Log</a>
        </div>
        
        <div class="info">
            <strong>Log Format:</strong> Timestamp | URL | HTTP Status | Content Length (bytes)
            <br>
            <strong>Note:</strong> Showing last 100 log entries (newest first)
        </div>
        
        <button class="refresh-button" onclick="location.reload()">Refresh Log</button>
        
        <div class="log-container">
            {% for line in log_lines %}
                {% if line.strip() %}
                    <div class="log-line">
                        {% set parts = line.split(' | ') %}
                        {% if parts|length >= 3 %}
                            <span class="timestamp">{{ parts[0] }}</span>
                            <span class="url">{{ parts[1] }}</span>
                            {% set status = parts[2].strip() %}
                            {% if status == '200' %}
                                <span class="status-200">{{ status }}</span>
                            {% elif status in ['ERROR', 'TIMEOUT'] %}
                                <span class="status-error">{{ status }}</span>
                            {% else %}
                                <span class="status-other">{{ status }}</span>
                            {% endif %}
                            {% if parts|length >= 4 %}
                                <span class="content-length">| {{ parts[3].strip() }} bytes</span>
                            {% endif %}
                        {% else %}
                            {{ line }}
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    
    <script>
        // Auto-refresh every 10 seconds
        setTimeout(function(){
            location.reload();
        }, 10000);
    </script>
</body>
</html>