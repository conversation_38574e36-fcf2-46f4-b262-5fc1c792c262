"""
Authentication and authorization security tests for multi-participant chat system.
Tests WebSocket auth, REST endpoint auth, multi-participant auth, token security, and rate limiting.
"""

import asyncio
import time
import uuid
import jwt
import hashlib
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import httpx
from aioresponses import aioresponses
import websockets
from websockets.exceptions import ConnectionClosed, InvalidHandshake

# Add parent directory to path
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../code/python'))

from chat.schemas import (
    ChatMessage, MessageType, MessageStatus,
    ParticipantInfo, ParticipantType
)


# Security Test Configuration
SECURITY_BASE_URL = "http://localhost:8080"
WEBSOCKET_BASE_URL = "ws://localhost:8080"
TEST_SECRET_KEY = "test_secret_key_for_jwt_signing_do_not_use_in_production"


class TokenGenerator:
    """Generate test tokens for security testing."""
    
    @staticmethod
    def create_valid_oauth_token(user_id: str = "test_user", expires_in: int = 3600) -> str:
        """Create valid OAuth token."""
        payload = {
            "sub": user_id,
            "email": f"{user_id}@example.com",
            "provider": "google",
            "iat": datetime.utcnow().timestamp(),
            "exp": (datetime.utcnow() + timedelta(seconds=expires_in)).timestamp(),
            "scope": "chat:read chat:write"
        }
        return f"Bearer {jwt.encode(payload, TEST_SECRET_KEY, algorithm='HS256')}"
    
    @staticmethod
    def create_valid_email_token(user_id: str = "email_user", expires_in: int = 3600) -> str:
        """Create valid email-based token."""
        payload = {
            "sub": user_id,
            "email": f"{user_id}@example.com",
            "provider": "email",
            "iat": datetime.utcnow().timestamp(),
            "exp": (datetime.utcnow() + timedelta(seconds=expires_in)).timestamp(),
            "scope": "chat:read chat:write"
        }
        return f"Bearer {jwt.encode(payload, TEST_SECRET_KEY, algorithm='HS256')}"
    
    @staticmethod
    def create_expired_token(user_id: str = "expired_user") -> str:
        """Create expired token."""
        payload = {
            "sub": user_id,
            "email": f"{user_id}@example.com",
            "provider": "google",
            "iat": (datetime.utcnow() - timedelta(hours=2)).timestamp(),
            "exp": (datetime.utcnow() - timedelta(hours=1)).timestamp(),  # Expired 1 hour ago
            "scope": "chat:read chat:write"
        }
        return f"Bearer {jwt.encode(payload, TEST_SECRET_KEY, algorithm='HS256')}"
    
    @staticmethod
    def create_invalid_signature_token(user_id: str = "invalid_user") -> str:
        """Create token with invalid signature."""
        payload = {
            "sub": user_id,
            "email": f"{user_id}@example.com",
            "provider": "google",
            "iat": datetime.utcnow().timestamp(),
            "exp": (datetime.utcnow() + timedelta(hours=1)).timestamp(),
            "scope": "chat:read chat:write"
        }
        return f"Bearer {jwt.encode(payload, 'wrong_secret_key', algorithm='HS256')}"
    
    @staticmethod  
    def create_insufficient_scope_token(user_id: str = "limited_user") -> str:
        """Create token with insufficient scope."""
        payload = {
            "sub": user_id,
            "email": f"{user_id}@example.com",
            "provider": "google",
            "iat": datetime.utcnow().timestamp(),
            "exp": (datetime.utcnow() + timedelta(hours=1)).timestamp(),
            "scope": "chat:read"  # Missing chat:write scope
        }
        return f"Bearer {jwt.encode(payload, TEST_SECRET_KEY, algorithm='HS256')}"


class MockWebSocketAuthClient:
    """Mock WebSocket client for authentication testing."""
    
    def __init__(self, token: str, conversation_id: str = "test_conv_001"):
        self.token = token
        self.conversation_id = conversation_id
        self.websocket = None
        self.is_connected = False
        self.connection_error = None
        
    async def connect_with_auth(self) -> bool:
        """Attempt WebSocket connection with authentication."""
        try:
            headers = {"Authorization": self.token}
            
            # Mock WebSocket connection with auth headers
            self.websocket = AsyncMock()
            self.websocket.send = AsyncMock()
            self.websocket.recv = AsyncMock()
            self.websocket.close = AsyncMock()
            self.websocket.closed = False
            
            # Simulate authentication check
            if "invalid" in self.token.lower() or "expired" in self.token.lower():
                raise InvalidHandshake(None, "Authentication failed")
            
            self.is_connected = True
            return True
            
        except InvalidHandshake as e:
            self.connection_error = str(e)
            return False
        except Exception as e:
            self.connection_error = str(e)
            return False
    
    async def send_authenticated_message(self, content: str) -> bool:
        """Send message through authenticated WebSocket."""
        if not self.is_connected:
            return False
            
        try:
            message = {
                "type": "message",
                "content": content,
                "conversation_id": self.conversation_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self.websocket.send(json.dumps(message))
            return True
            
        except Exception:
            return False
    
    async def disconnect(self):
        """Disconnect WebSocket."""
        if self.websocket:
            await self.websocket.close()
        self.is_connected = False


@pytest.fixture
async def security_client():
    """Create HTTP client for security testing."""
    async with httpx.AsyncClient(
        base_url=SECURITY_BASE_URL,
        timeout=httpx.Timeout(30.0)
    ) as client:
        yield client


@pytest.fixture
def token_generator():
    """Create token generator for tests."""
    return TokenGenerator()


@pytest.fixture
def valid_oauth_token(token_generator):
    """Valid OAuth token fixture."""
    return token_generator.create_valid_oauth_token()


@pytest.fixture
def valid_email_token(token_generator):
    """Valid email token fixture."""
    return token_generator.create_valid_email_token()


@pytest.fixture
def expired_token(token_generator):
    """Expired token fixture."""
    return token_generator.create_expired_token()


@pytest.fixture
def invalid_token(token_generator):
    """Invalid token fixture."""
    return token_generator.create_invalid_signature_token()


@pytest.mark.security
@pytest.mark.asyncio
class TestWebSocketAuthentication:
    """Test WebSocket authentication security."""
    
    async def test_valid_token_accepts_connection(self, valid_oauth_token):
        """Test that valid token accepts WebSocket connection."""
        client = MockWebSocketAuthClient(valid_oauth_token)
        
        success = await client.connect_with_auth()
        
        assert success is True
        assert client.is_connected is True
        assert client.connection_error is None
        
        # Verify can send authenticated messages
        message_sent = await client.send_authenticated_message("Test authenticated message")
        assert message_sent is True
        
        await client.disconnect()
    
    async def test_invalid_token_rejects_immediately(self, invalid_token):
        """Test that invalid token rejects connection immediately."""
        client = MockWebSocketAuthClient(invalid_token)
        
        success = await client.connect_with_auth()
        
        assert success is False
        assert client.is_connected is False
        assert "Authentication failed" in client.connection_error
    
    async def test_expired_token_handling(self, expired_token):
        """Test expired token handling in WebSocket."""
        client = MockWebSocketAuthClient(expired_token)
        
        success = await client.connect_with_auth()
        
        assert success is False
        assert client.is_connected is False
        assert client.connection_error is not None
    
    async def test_token_refresh_during_connection(self, valid_oauth_token, token_generator):
        """Test token refresh during active WebSocket connection."""
        client = MockWebSocketAuthClient(valid_oauth_token)
        
        # Initial connection
        success = await client.connect_with_auth()
        assert success is True
        
        # Simulate token refresh
        new_token = token_generator.create_valid_oauth_token("refreshed_user")
        client.token = new_token
        
        # Connection should remain active with new token
        message_sent = await client.send_authenticated_message("Message after token refresh")
        assert message_sent is True
        
        await client.disconnect()
    
    async def test_session_hijacking_prevention(self, valid_oauth_token):
        """Test prevention of session hijacking."""
        # Create two clients with same token (simulating hijacking attempt)
        client_original = MockWebSocketAuthClient(valid_oauth_token, "conv_original")
        client_hijacker = MockWebSocketAuthClient(valid_oauth_token, "conv_hijacked")
        
        # Original client connects
        success_original = await client_original.connect_with_auth()
        assert success_original is True
        
        # Hijacker attempts connection with same token to different conversation
        success_hijacker = await client_hijacker.connect_with_auth()
        
        # Both should be allowed (same user, different conversations)
        # But each should only access their own conversation
        assert success_hijacker is True
        
        # Verify conversation isolation
        assert client_original.conversation_id != client_hijacker.conversation_id
        
        await client_original.disconnect()
        await client_hijacker.disconnect()
    
    async def test_websocket_auth_with_different_providers(self, token_generator):
        """Test WebSocket auth with different OAuth providers."""
        providers_tokens = [
            ("google", token_generator.create_valid_oauth_token("google_user")),
            ("email", token_generator.create_valid_email_token("email_user"))
        ]
        
        for provider, token in providers_tokens:
            client = MockWebSocketAuthClient(token, f"conv_{provider}")
            
            success = await client.connect_with_auth()
            
            assert success is True, f"Connection failed for {provider} provider"
            assert client.is_connected is True
            
            # Verify can send messages regardless of provider
            message_sent = await client.send_authenticated_message(f"Message from {provider} user")
            assert message_sent is True
            
            await client.disconnect()


@pytest.mark.security
@pytest.mark.asyncio
class TestRESTEndpointAuth:
    """Test REST endpoint authentication security."""
    
    async def test_all_endpoints_require_auth(self, security_client):
        """Test that all endpoints require authentication."""
        protected_endpoints = [
            ("GET", "/chat/my-conversations"),
            ("POST", "/chat/create"),
            ("GET", "/chat/conversations/test_conv_001"),
            ("POST", "/chat/test_conv_001/join"),
            ("DELETE", "/chat/test_conv_001/leave"),
            ("POST", "/chat/test_conv_001/message")
        ]
        
        with aioresponses() as mock_resp:
            # Mock 401 responses for all endpoints without auth
            for method, endpoint in protected_endpoints:
                if method == "GET":
                    mock_resp.get(
                        f"{SECURITY_BASE_URL}{endpoint}",
                        payload={"error": "Authentication required", "code": "MISSING_AUTH"},
                        status=401
                    )
                elif method == "POST":
                    mock_resp.post(
                        f"{SECURITY_BASE_URL}{endpoint}",
                        payload={"error": "Authentication required", "code": "MISSING_AUTH"},
                        status=401
                    )
                elif method == "DELETE":
                    mock_resp.delete(
                        f"{SECURITY_BASE_URL}{endpoint}",
                        payload={"error": "Authentication required", "code": "MISSING_AUTH"},
                        status=401
                    )
            
            # Test each endpoint without authorization header
            for method, endpoint in protected_endpoints:
                if method == "GET":
                    response = await security_client.get(endpoint)
                elif method == "POST":
                    response = await security_client.post(endpoint, json={})
                elif method == "DELETE":
                    response = await security_client.delete(endpoint)
                
                assert response.status_code == 401, f"Endpoint {method} {endpoint} should require auth"
                assert "Authentication required" in response.json().get("error", "")
    
    async def test_token_validation_on_every_request(self, security_client, valid_oauth_token, invalid_token):
        """Test token validation on every request."""
        test_endpoint = "/chat/my-conversations"
        
        with aioresponses() as mock_resp:
            # Mock valid token response
            mock_resp.get(
                f"{SECURITY_BASE_URL}{test_endpoint}",
                payload=[{"id": "conv_001", "title": "Valid Token Conversation"}],
                status=200
            )
            
            # Mock invalid token response
            mock_resp.get(
                f"{SECURITY_BASE_URL}{test_endpoint}",
                payload={"error": "Invalid token", "code": "INVALID_TOKEN"},
                status=401
            )
            
            # Test with valid token
            valid_headers = {"Authorization": valid_oauth_token}
            valid_response = await security_client.get(test_endpoint, headers=valid_headers)
            
            assert valid_response.status_code == 200
            
            # Test with invalid token
            invalid_headers = {"Authorization": invalid_token}
            invalid_response = await security_client.get(test_endpoint, headers=invalid_headers)
            
            assert invalid_response.status_code == 401
            assert "Invalid token" in invalid_response.json().get("error", "")
    
    async def test_cross_user_access_prevention(self, security_client, token_generator):
        """Test prevention of cross-user access."""
        user1_token = token_generator.create_valid_oauth_token("user1")
        user2_token = token_generator.create_valid_oauth_token("user2")
        
        with aioresponses() as mock_resp:
            # Mock user1's conversations
            mock_resp.get(
                f"{SECURITY_BASE_URL}/chat/my-conversations",
                payload=[{"id": "user1_conv_001", "title": "User 1 Conversation"}],
                status=200
            )
            
            # Mock accessing user1's conversation with user2's token (should fail)
            mock_resp.get(
                f"{SECURITY_BASE_URL}/chat/conversations/user1_conv_001",
                payload={"error": "Access denied", "code": "FORBIDDEN"},
                status=403
            )
            
            # User1 can access their own conversations
            user1_headers = {"Authorization": user1_token}
            user1_response = await security_client.get("/chat/my-conversations", headers=user1_headers)
            
            assert user1_response.status_code == 200
            
            # User2 cannot access user1's specific conversation
            user2_headers = {"Authorization": user2_token}
            cross_access_response = await security_client.get("/chat/conversations/user1_conv_001", headers=user2_headers)
            
            assert cross_access_response.status_code == 403
            assert "Access denied" in cross_access_response.json().get("error", "")
    
    async def test_admin_vs_regular_user_permissions(self, security_client, token_generator):
        """Test admin vs regular user permissions."""
        regular_token = token_generator.create_valid_oauth_token("regular_user")
        
        # Create admin token with special scope
        admin_payload = {
            "sub": "admin_user",
            "email": "<EMAIL>",
            "provider": "google",
            "iat": datetime.utcnow().timestamp(),
            "exp": (datetime.utcnow() + timedelta(hours=1)).timestamp(),
            "scope": "chat:read chat:write chat:admin",
            "role": "admin"
        }
        admin_token = f"Bearer {jwt.encode(admin_payload, TEST_SECRET_KEY, algorithm='HS256')}"
        
        with aioresponses() as mock_resp:
            # Mock admin-only endpoint
            admin_endpoint = "/chat/admin/metrics"
            
            # Regular user denied
            mock_resp.get(
                f"{SECURITY_BASE_URL}{admin_endpoint}",
                payload={"error": "Admin access required", "code": "INSUFFICIENT_PERMISSIONS"},
                status=403
            )
            
            # Admin user allowed
            mock_resp.get(
                f"{SECURITY_BASE_URL}{admin_endpoint}",
                payload={"total_conversations": 142, "active_users": 89},
                status=200
            )
            
            # Test regular user access to admin endpoint
            regular_headers = {"Authorization": regular_token}
            regular_response = await security_client.get(admin_endpoint, headers=regular_headers)
            
            assert regular_response.status_code == 403
            
            # Test admin user access
            admin_headers = {"Authorization": admin_token}
            admin_response = await security_client.get(admin_endpoint, headers=admin_headers)
            
            assert admin_response.status_code == 200
            assert "total_conversations" in admin_response.json()


@pytest.mark.security
@pytest.mark.asyncio
class TestMultiParticipantAuth:
    """Test multi-participant authentication security."""
    
    async def test_each_human_authenticates_independently(self, security_client, token_generator):
        """Test that each human authenticates independently."""
        # Create tokens for 3 different users
        user_tokens = [
            token_generator.create_valid_oauth_token(f"multi_user_{i}")
            for i in range(3)
        ]
        
        conversation_id = "multi_auth_conv_001"
        
        with aioresponses() as mock_resp:
            # Mock conversation creation by first user
            mock_resp.post(
                f"{SECURITY_BASE_URL}/chat/create",
                payload={"id": conversation_id, "creator": "multi_user_0"},
                status=201
            )
            
            # Mock join requests by other users
            for i in range(1, 3):
                mock_resp.post(
                    f"{SECURITY_BASE_URL}/chat/{conversation_id}/join",
                    payload={"success": True, "participant_id": f"multi_user_{i}"},
                    status=200
                )
            
            # First user creates conversation
            creator_headers = {"Authorization": user_tokens[0]}
            create_response = await security_client.post(
                "/chat/create",
                json={
                    "title": "Multi-participant Auth Test",
                    "sites": ["example.com"],
                    "participant": {"participantId": "multi_user_0", "displayName": "User 0"}
                },
                headers=creator_headers
            )
            
            assert create_response.status_code == 201
            
            # Other users join with their own tokens
            for i in range(1, 3):
                join_headers = {"Authorization": user_tokens[i]}
                join_response = await security_client.post(
                    f"/chat/{conversation_id}/join",
                    json={"participant": {"participantId": f"multi_user_{i}", "displayName": f"User {i}"}},
                    headers=join_headers
                )
                
                assert join_response.status_code == 200
                assert f"multi_user_{i}" in join_response.json().get("participant_id", "")
    
    async def test_cannot_impersonate_other_participants(self, security_client, token_generator):
        """Test that users cannot impersonate other participants."""
        user1_token = token_generator.create_valid_oauth_token("impersonate_user1")
        user2_token = token_generator.create_valid_oauth_token("impersonate_user2")
        
        conversation_id = "impersonate_conv_001"
        
        with aioresponses() as mock_resp:
            # Mock message sending with impersonation attempt
            mock_resp.post(
                f"{SECURITY_BASE_URL}/chat/{conversation_id}/message",
                payload={"error": "Cannot impersonate other users", "code": "IMPERSONATION_DENIED"},
                status=403
            )
            
            # User1 attempts to send message as User2 (impersonation)
            user1_headers = {"Authorization": user1_token}
            impersonate_response = await security_client.post(
                f"/chat/{conversation_id}/message",
                json={
                    "content": "Attempting impersonation",
                    "sender_id": "impersonate_user2",  # Wrong sender ID
                    "participant_override": "impersonate_user2"
                },
                headers=user1_headers
            )
            
            assert impersonate_response.status_code == 403
            assert "Cannot impersonate" in impersonate_response.json().get("error", "")
    
    async def test_join_requests_validate_permissions(self, security_client, token_generator):
        """Test that join requests validate permissions properly."""
        owner_token = token_generator.create_valid_oauth_token("conv_owner")
        joiner_token = token_generator.create_valid_oauth_token("conv_joiner")
        
        private_conversation_id = "private_conv_001"
        
        with aioresponses() as mock_resp:
            # Mock private conversation (invite-only)
            mock_resp.post(
                f"{SECURITY_BASE_URL}/chat/{private_conversation_id}/join",
                payload={"error": "Invitation required", "code": "INVITATION_REQUIRED"},
                status=403
            )
            
            # Mock with valid invitation
            mock_resp.post(
                f"{SECURITY_BASE_URL}/chat/{private_conversation_id}/join",
                payload={"success": True, "joined_via": "invitation"},
                status=200
            )
            
            # Attempt to join private conversation without invitation
            joiner_headers = {"Authorization": joiner_token}
            unauthorized_join = await security_client.post(
                f"/chat/{private_conversation_id}/join",
                json={"participant": {"participantId": "conv_joiner", "displayName": "Joiner"}},
                headers=joiner_headers
            )
            
            assert unauthorized_join.status_code == 403
            assert "Invitation required" in unauthorized_join.json().get("error", "")
            
            # Join with valid invitation token
            invited_join = await security_client.post(
                f"/chat/{private_conversation_id}/join", 
                json={
                    "participant": {"participantId": "conv_joiner", "displayName": "Joiner"},
                    "invitation_token": "valid_invitation_123"
                },
                headers=joiner_headers
            )
            
            assert invited_join.status_code == 200
    
    async def test_participant_removal_authorization(self, security_client, token_generator):
        """Test authorization for participant removal."""
        owner_token = token_generator.create_valid_oauth_token("removal_owner")
        participant_token = token_generator.create_valid_oauth_token("removal_participant")
        other_user_token = token_generator.create_valid_oauth_token("removal_other")
        
        conversation_id = "removal_conv_001"
        
        with aioresponses() as mock_resp:
            # Mock owner can remove participants
            mock_resp.delete(
                f"{SECURITY_BASE_URL}/chat/{conversation_id}/participants/removal_participant",
                payload={"success": True, "removed_by": "owner"},
                status=200
            )
            
            # Mock non-owner cannot remove others
            mock_resp.delete(
                f"{SECURITY_BASE_URL}/chat/{conversation_id}/participants/removal_participant",
                payload={"error": "Only owner can remove participants", "code": "INSUFFICIENT_PERMISSIONS"},
                status=403
            )
            
            # Owner can remove participant
            owner_headers = {"Authorization": owner_token}
            owner_removal = await security_client.delete(
                f"/chat/{conversation_id}/participants/removal_participant",
                headers=owner_headers
            )
            
            assert owner_removal.status_code == 200
            
            # Other user cannot remove participants
            other_headers = {"Authorization": other_user_token}
            unauthorized_removal = await security_client.delete(
                f"/chat/{conversation_id}/participants/removal_participant",
                headers=other_headers
            )
            
            assert unauthorized_removal.status_code == 403
            assert "Only owner can remove" in unauthorized_removal.json().get("error", "")


@pytest.mark.security
@pytest.mark.asyncio
class TestTokenSecurity:
    """Test token security measures."""
    
    async def test_no_tokens_in_urls(self, security_client, valid_oauth_token):
        """Test that tokens are never exposed in URLs."""
        # Simulate URL with token (security anti-pattern)
        insecure_url = f"/chat/my-conversations?token={valid_oauth_token.split(' ')[1]}"
        
        with aioresponses() as mock_resp:
            # Mock server rejection of token in URL
            mock_resp.get(
                f"{SECURITY_BASE_URL}{insecure_url}",
                payload={"error": "Token in URL not allowed", "code": "INSECURE_TOKEN_TRANSPORT"},
                status=400
            )
            
            # Test that server rejects token in URL
            response = await security_client.get(insecure_url)
            
            assert response.status_code == 400
            assert "Token in URL not allowed" in response.json().get("error", "")
    
    async def test_secure_storage_sessionStorage(self):
        """Test that tokens should be stored in sessionStorage (client-side test)."""
        # This test documents the requirement - actual implementation is client-side
        
        # Token storage security requirements:
        storage_requirements = {
            "preferred": "sessionStorage",  # Cleared when tab closes
            "acceptable": "secure cookie with HttpOnly",
            "forbidden": ["localStorage", "URL parameters", "local files"]
        }
        
        # Verify requirements are documented
        assert storage_requirements["preferred"] == "sessionStorage"
        assert "localStorage" in storage_requirements["forbidden"]
        assert "URL parameters" in storage_requirements["forbidden"]
    
    async def test_token_rotation_support(self, security_client, token_generator):
        """Test token rotation support."""
        original_token = token_generator.create_valid_oauth_token("rotation_user")
        
        # Simulate token rotation endpoint
        with aioresponses() as mock_resp:
            mock_resp.post(
                f"{SECURITY_BASE_URL}/auth/rotate-token",
                payload={
                    "new_token": token_generator.create_valid_oauth_token("rotation_user", expires_in=7200),
                    "expires_in": 7200
                },
                status=200
            )
            
            # Request token rotation
            rotation_headers = {"Authorization": original_token}
            rotation_response = await security_client.post(
                "/auth/rotate-token",
                headers=rotation_headers
            )
            
            assert rotation_response.status_code == 200
            assert "new_token" in rotation_response.json()
            assert "expires_in" in rotation_response.json()
    
    async def test_logout_clears_all_tokens(self, security_client, valid_oauth_token):
        """Test that logout clears all associated tokens."""
        with aioresponses() as mock_resp:
            # Mock logout endpoint
            mock_resp.post(
                f"{SECURITY_BASE_URL}/auth/logout",
                payload={"success": True, "tokens_invalidated": 3},
                status=200
            )
            
            # Mock using token after logout (should fail)
            mock_resp.get(
                f"{SECURITY_BASE_URL}/chat/my-conversations",
                payload={"error": "Token invalidated", "code": "TOKEN_INVALIDATED"},
                status=401
            )
            
            # Perform logout
            logout_headers = {"Authorization": valid_oauth_token}
            logout_response = await security_client.post("/auth/logout", headers=logout_headers)
            
            assert logout_response.status_code == 200
            assert logout_response.json()["success"] is True
            
            # Verify token is invalidated after logout
            post_logout_response = await security_client.get("/chat/my-conversations", headers=logout_headers)
            
            assert post_logout_response.status_code == 401
            assert "Token invalidated" in post_logout_response.json().get("error", "")


@pytest.mark.security
@pytest.mark.asyncio
class TestRateLimiting:
    """Test rate limiting security measures."""
    
    async def test_per_user_connection_limits(self, token_generator):
        """Test per-user connection limits."""
        user_token = token_generator.create_valid_oauth_token("connection_limit_user")
        connection_limit = 5  # Max 5 connections per user
        
        # Create multiple WebSocket clients for same user
        clients = []
        for i in range(connection_limit + 2):  # Attempt 2 more than limit
            client = MockWebSocketAuthClient(user_token, f"conv_limit_{i}")
            clients.append(client)
        
        # Connect all clients
        connection_results = []
        for i, client in enumerate(clients):
            success = await client.connect_with_auth()
            connection_results.append(success)
        
        # First 5 should succeed, rest should fail
        successful_connections = sum(connection_results)
        
        # In real implementation, would enforce actual connection limits
        # For testing, document the requirement
        assert connection_limit == 5, "Connection limit should be enforced per user"
        
        # Cleanup
        for client in clients:
            await client.disconnect()
    
    async def test_message_rate_throttling(self, security_client, valid_oauth_token):
        """Test message rate throttling."""
        conversation_id = "rate_limit_conv"
        rate_limit = 10  # 10 messages per minute
        
        with aioresponses() as mock_resp:
            # Mock successful messages up to rate limit
            for i in range(rate_limit):
                mock_resp.post(
                    f"{SECURITY_BASE_URL}/chat/{conversation_id}/message",
                    payload={"success": True, "message_id": f"msg_{i}"},
                    status=200
                )
            
            # Mock rate limit exceeded
            mock_resp.post(
                f"{SECURITY_BASE_URL}/chat/{conversation_id}/message",
                payload={
                    "error": "Rate limit exceeded", 
                    "code": "RATE_LIMITED",
                    "retry_after": 60,
                    "limit": "10 messages per minute"
                },
                status=429
            )
            
            # Send messages up to rate limit
            headers = {"Authorization": valid_oauth_token}
            message_responses = []
            
            for i in range(rate_limit + 1):  # One more than limit
                response = await security_client.post(
                    f"/chat/{conversation_id}/message",
                    json={"content": f"Rate limit test message {i}"},
                    headers=headers
                )
                message_responses.append(response)
            
            # First messages should succeed
            for i in range(rate_limit):
                assert message_responses[i].status_code == 200
            
            # Last message should be rate limited
            rate_limited_response = message_responses[-1]
            assert rate_limited_response.status_code == 429
            assert "Rate limit exceeded" in rate_limited_response.json().get("error", "")
            assert "retry_after" in rate_limited_response.json()
    
    async def test_queue_overflow_returns_429(self, security_client, valid_oauth_token):
        """Test that queue overflow returns 429 status."""
        conversation_id = "queue_overflow_conv"
        
        with aioresponses() as mock_resp:
            # Mock queue overflow response
            mock_resp.post(
                f"{SECURITY_BASE_URL}/chat/{conversation_id}/message",
                payload={
                    "error": "Conversation queue is full",
                    "code": "QUEUE_FULL",
                    "retry_after": 30,
                    "queue_size": 1000,
                    "queue_limit": 1000
                },
                status=429
            )
            
            # Send message to full queue
            headers = {"Authorization": valid_oauth_token}
            overflow_response = await security_client.post(
                f"/chat/{conversation_id}/message",
                json={"content": "Queue overflow test message"},
                headers=headers
            )
            
            assert overflow_response.status_code == 429
            assert "queue is full" in overflow_response.json().get("error", "").lower()
            assert "retry_after" in overflow_response.json()
            assert "queue_size" in overflow_response.json()
    
    async def test_exponential_backoff_enforcement(self, security_client, valid_oauth_token):
        """Test exponential backoff enforcement for repeated failures."""
        endpoint = "/chat/backoff_test/message"
        
        # Simulate exponential backoff: 1s, 2s, 4s, 8s, 16s, 30s (max)
        backoff_times = [1, 2, 4, 8, 16, 30]
        
        with aioresponses() as mock_resp:
            # Mock responses with increasing backoff times
            for i, backoff in enumerate(backoff_times):
                mock_resp.post(
                    f"{SECURITY_BASE_URL}{endpoint}",
                    payload={
                        "error": "Service temporarily unavailable",
                        "code": "SERVICE_UNAVAILABLE",
                        "retry_after": backoff,
                        "attempt": i + 1
                    },
                    status=429
                )
            
            # Test exponential backoff responses
            headers = {"Authorization": valid_oauth_token}
            
            for i, expected_backoff in enumerate(backoff_times):
                response = await security_client.post(
                    endpoint,
                    json={"content": f"Backoff test attempt {i + 1}"},
                    headers=headers
                )
                
                assert response.status_code == 429
                actual_backoff = response.json().get("retry_after")
                assert actual_backoff == expected_backoff, f"Attempt {i + 1}: expected {expected_backoff}s, got {actual_backoff}s"
                
                # In real implementation, would enforce actual waiting
                # For testing, verify the backoff values are correct
                if i < len(backoff_times) - 1:  # Not the last attempt
                    assert actual_backoff < backoff_times[i + 1] * 2, "Backoff should be exponential"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "security"])