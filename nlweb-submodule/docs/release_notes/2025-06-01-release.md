# Release Notes Summary - Microsoft/NLWeb
Updates since June 24, 2025

## 🔒 Security Improvements
- **Fix path traversal vulnerability in static file handler (#231)** - Added security checks to prevent directory traversal attacks, file extension whitelist, and logging for security events.
- **Checking URL in a more secure way in streaming.js (#236)** - Improved URL validation to prevent domain spoofing attacks by using proper URL parsing instead of unsafe `startsWith` checks.

## 🔧 Code Quality & Refactoring
- **Gemini Developer API now fixed (#232)** - Updated Gemini integration to use the Developer API. (Note that Vertex API is not supported currently as its API follows a different authentication pattern than we are using today.)  This fixes issues #138, #107, and #112.
- **Modular Code Refactor (#244)** - There is a new folder called 'refactored' with the new architecture.  With this refactor, there are fewer file changes required to add new providers or tools, which we going forward will refer to as 'methods' to avoid confusion with the term 'tools' as used in other contexts such as MCP. These accordingly appear in the new 'methods' folder.  We plan to cut the repo over to using this new architecture in the coming couple of weeks after we get things tested & stabilized. 

## ✨ New Features
- **Elasticsearch retrieval (#229)** - Added Elasticsearch retrieval functionality with [documentation](https://github.com/microsoft/NLWeb/blob/main/docs/setup-elasticsearch.md).
- **Stats query handler (#226)** - New statistics query handling capability for visualizing data using Data Commons. You can learn to use this tool via the [Statistics Tool Documentation](https://github.com/microsoft/NLWeb/blob/main/docs/statistics_tool_documentation.md) provided, including sample query patterns.

## 📝 Documentation & Maintenance
- **Documentation name update (#240)** - Some files had been added in different PRs that didn't match the standard naming conventions.  These were updated to the standard format and fixed links throughout repo.
