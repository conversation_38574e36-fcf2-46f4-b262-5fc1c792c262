# Docker Environment Configuration Template
# Copy this file to .env.docker and fill in your values

# Qdrant Configuration (automatically set by docker-compose)
QDRANT_URL=http://qdrant:6333
QDRANT_API_KEY=

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=0

# Add any additional environment variables your crawler needs
# For example, if you need API keys for embedding providers:

# OpenAI API Key (if using OpenAI embeddings)
# OPENAI_API_KEY=your_openai_api_key_here

# Azure OpenAI Configuration (if using Azure OpenAI)
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_KEY=your_azure_openai_key_here

# Other provider configurations as needed...
