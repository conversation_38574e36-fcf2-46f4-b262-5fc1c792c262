# NLWeb Rest API

At this point, NLWeb supports 2 APIs at the endpoints /ask and /mcp. The arguments are the same for both, as is most of the functionality. The /mcp endpoint returns the answers in format that MCP clients can use. The /mcp endpoint also supports the core MCP methods (`list_tools`, `list_prompts`, `call_tool` and `get_prompt`).

In the included implementation, there is no server side state. So, the context of the conversation thus far has to be passed back as part of the request. As described below, there are multiple ways to do this.

The required argument to ask / mcp is:

- `query`: The current query in natural language

The following are optional arguments:

- `site`: A token corresponding to some subset of the data of a backend. For example, a backend might support multiple sites, each with a conversational interface restricted to its content. This parameter can be used to specify the site
- `prev`: comma separated list of previous queries. In most cases, the decontextualized query can be constructed from this.
- `decontextualized_query`: the entire decontextualized query. If this is available, no decontextualization is done on the server side
- `streaming`: defaults to true. To turn off streaming, specify a value of 0 or false
- `query_id`: if none is specified, one will be auto generated
- `mode`: values are list (default), summarize and generate
    - `list` : returns the list of top matches from the backend that are most relevant to the query
    - `summarize`: summarizes the list and presents the summary and also returns the list
    - `generate`: much more like traditional RAG, where the list is generated and one or more calls are made to an LLM to try answer the user's question.

The returned value is a json object with the following fields:

- `query_id`
- zero or more result attributes
- An array (soon to be moved to a richer structure, starting with schema.org's ItemList) of results, each of which has:
    - `url`
    - `name`
    - `site`
    - `score`
    - `description` (generated by an llm)
    - `schema_object` (the item, from the data store, encoded in json)
