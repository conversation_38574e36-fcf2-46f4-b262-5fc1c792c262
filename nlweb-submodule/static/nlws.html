<html>
<style>
 .chat-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: sans-serif;
  }
</style>
</style>
<script type="module">
  // Import styles first
  import { applyStyles } from './styles.js';
  applyStyles();
  
  // Import the ChatInterface from the correct module
  import { ChatInterface } from './chat-interface.js';
  
  // Make it available globally
  window.ChatInterface = ChatInterface;
</script>

<body>
  <div id="ai-search-container" style="position: relative; width: 100%; max-width: 800px; margin: 20px auto;">
    <div class="search-box" style="display: flex; gap: 10px; padding: 10px;">
         <input 
              type="text" 
              id="ai-search-input"
              autocomplete="on"
              placeholder="NLWeb Chat" 
              style="flex-grow: 1; padding: 12px; border: 2px solid #FFD12F; border-radius: 8px; font-size: 16px;"
         >
         <button 
              id="ai-search-button"
              style="padding: 12px 24px; background: #FFD12F; border: none; border-radius: 8px; color: #1B4D7A; font-weight: bold; cursor: pointer;"
         >
              Ask
         </button>
    </div>
    <div id="chat-container" 
         style="display: none; position: absolute; top: 100%; left: 0; right: 0; z-index: 1000; background: white; box-shadow: 0 4px 12px rgba(0,0,0,0.1); border-radius: 8px; margin-top: 8px; height: 600px;">
    <div id="close-icon" style="position: absolute; top: 10px; right: 10px; cursor: pointer; padding: 5px;">
      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
           <line x1="18" y1="6" x2="6" y2="18"></line>
           <line x1="6" y1="6" x2="18" y2="18"></line>
         </svg>
    </div>
    <script>
         document.getElementById('close-icon').addEventListener('click', () => {
              document.getElementById('chat-container').style.display = 'none';
         });
    </script>
    </div>
</div>

<script>
   

    document.addEventListener('DOMContentLoaded', () => {
         const searchInput = document.getElementById('ai-search-input');
         const searchButton = document.getElementById('ai-search-button');
         var chatContainer = document.getElementById('chat-container');
         searchButton.addEventListener('click', handleSearch);
         searchInput.addEventListener('keypress', (e) => {
              if (e.key === 'Enter') {
                   handleSearch();
              }
         });

         var chat_interface = null;

         function findChatInterface() {
          if (chat_interface) {
              return chat_interface;
          }
          chat_interface = new ChatInterface('nlws', display_mode='nlwebsearch', generate_mode='generate');
          return chat_interface;
         }


         function handleSearch() {
              const query = searchInput.value.trim();
              console.log(query);
              
              if (!query) {
                  console.log("No query");
                  return;
              }

              chatContainer.style.display = 'block';
              chat_interface = findChatInterface();
              searchInput.value = '';
           //   chat_interface.clearHistory();
              chat_interface.sendMessage(query);
         }

         // Close results when clicking outside
         document.addEventListener('click', (e) => {
              if (!e.target.closest('#chat-container')) {
                  //console.log("closing chat")
                  //  chatContainer.style.display = 'none';
              }
         });
        // chatContainer.style.display = 'block';
    });
</script>


  <div id="chat-container"  style="position: relative; width: 100%; max-width: 800px; margin: 20px auto; font-family: sans-serif;"></div>
</body>

</html>
