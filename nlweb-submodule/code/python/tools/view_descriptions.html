<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Store Descriptions Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        
        .store-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .store-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .store-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .store-card h2 {
            color: #764ba2;
            margin-bottom: 10px;
            font-size: 1.5em;
        }
        
        .store-category {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            margin-bottom: 10px;
        }
        
        .store-url {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
            word-break: break-all;
        }
        
        .store-stats {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #764ba2;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #999;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .modal-content {
            background: white;
            margin: 40px auto;
            padding: 40px;
            width: 90%;
            max-width: 1200px;
            border-radius: 12px;
            position: relative;
        }
        
        .close-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 2em;
            color: #999;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .close-btn:hover {
            color: #333;
        }
        
        .modal h2 {
            color: #764ba2;
            margin-bottom: 20px;
            font-size: 2em;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .section-content {
            white-space: pre-wrap;
            line-height: 1.8;
        }
        
        .tab-container {
            margin-top: 20px;
        }
        
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .tab {
            padding: 10px 20px;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
            margin-bottom: -2px;
        }
        
        .tab.active {
            color: #764ba2;
            border-bottom-color: #764ba2;
            font-weight: bold;
        }
        
        .tab:hover {
            color: #764ba2;
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.5s;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .loading {
            text-align: center;
            color: white;
            font-size: 1.2em;
            margin: 40px 0;
        }
        
        .error {
            background: #ff6b6b;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .input-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .input-section label {
            display: block;
            margin-bottom: 10px;
            color: #764ba2;
            font-weight: bold;
        }
        
        .input-section input {
            width: 100%;
            padding: 10px;
            border: 2px solid #eee;
            border-radius: 6px;
            font-size: 1em;
        }
        
        .input-section button {
            background: #764ba2;
            color: white;
            border: none;
            padding: 10px 30px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin-top: 10px;
            transition: background 0.3s;
        }
        
        .input-section button:hover {
            background: #5a3785;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ Shopify Store Product Catalog Viewer</h1>
        
        <div class="input-section">
            <label for="jsonl-input">Load JSONL File:</label>
            <input type="file" id="jsonl-input" accept=".jsonl">
            <button onclick="loadFile()">Load Descriptions</button>
        </div>
        
        <div id="loading" class="loading" style="display:none;">Loading store descriptions...</div>
        <div id="error" class="error" style="display:none;"></div>
        
        <div id="store-grid" class="store-grid"></div>
    </div>
    
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal()">&times;</span>
            <h2 id="modal-title"></h2>
            
            <div class="tab-container">
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('overview')">Overview</button>
                    <button class="tab" onclick="switchTab('analysis')">URL Analysis</button>
                    <button class="tab" onclick="switchTab('description')">Full Description</button>
                    <button class="tab" onclick="switchTab('raw')">Raw Data</button>
                </div>
                
                <div id="overview" class="tab-content active">
                    <div class="section">
                        <h3>Store Information</h3>
                        <div id="store-info" class="section-content"></div>
                    </div>
                </div>
                
                <div id="analysis" class="tab-content">
                    <div class="section">
                        <h3>Sitemap URL Analysis</h3>
                        <div id="url-analysis" class="section-content"></div>
                    </div>
                </div>
                
                <div id="description" class="tab-content">
                    <div class="section">
                        <h3>Product Catalog Description</h3>
                        <div id="full-description" class="section-content"></div>
                    </div>
                </div>
                
                <div id="raw" class="tab-content">
                    <div class="section">
                        <h3>Raw JSON Data</h3>
                        <pre id="raw-data" class="section-content" style="overflow-x: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let stores = [];
        
        function loadFile() {
            const input = document.getElementById('jsonl-input');
            const file = input.files[0];
            
            if (!file) {
                showError('Please select a JSONL file');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const lines = e.target.result.split('\n').filter(line => line.trim());
                    stores = lines.map(line => {
                        const store = JSON.parse(line);
                        console.log('Parsing store:', store.name);
                        console.log('Keys in store object:', Object.keys(store));
                        console.log('Has detailed_description?', 'detailed_description' in store);
                        return store;
                    });
                    console.log('Loaded stores:', stores);
                    console.log('First store:', stores[0]);
                    console.log('First store detailed_description length:', 
                        stores[0]?.detailed_description?.length || 'No description');
                    displayStores();
                    document.getElementById('loading').style.display = 'none';
                    showError(`Successfully loaded ${stores.length} stores`);
                } catch (err) {
                    showError('Error parsing JSONL file: ' + err.message);
                    console.error('Parse error:', err);
                }
            };
            
            document.getElementById('loading').style.display = 'block';
            reader.readAsText(file);
        }
        
        function displayStores() {
            const grid = document.getElementById('store-grid');
            grid.innerHTML = '';
            
            stores.forEach((store, index) => {
                const card = document.createElement('div');
                card.className = 'store-card';
                card.onclick = () => showModal(index);
                
                const descLength = (store.detailed_description || '').length;
                const analysisLength = (store.sitemap_analysis || '').length;
                
                card.innerHTML = `
                    <h2>${store.name || 'Unknown Store'}</h2>
                    <span class="store-category">${store.category || 'General'}</span>
                    <div class="store-url">${store.url || ''}</div>
                    <p>${store.description || 'No description available'}</p>
                    
                    <div class="store-stats">
                        <div class="stat">
                            <span class="stat-value">${store.processing_metadata?.sitemaps_found || 0}</span>
                            <span class="stat-label">Sitemaps</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${store.processing_metadata?.urls_analyzed || 0}</span>
                            <span class="stat-label">URLs</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${Math.round(descLength / 6)}</span>
                            <span class="stat-label">Words</span>
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }
        
        function showModal(index) {
            const store = stores[index];
            const modal = document.getElementById('modal');
            
            console.log('Opening modal for store:', store.name);
            console.log('Store has detailed_description:', !!store.detailed_description);
            console.log('Description length:', store.detailed_description?.length || 0);
            
            document.getElementById('modal-title').textContent = store.name || 'Store Details';
            
            // Overview tab
            document.getElementById('store-info').innerHTML = `
<strong>Name:</strong> ${store.name || 'N/A'}
<strong>URL:</strong> ${store.url || 'N/A'}
<strong>Category:</strong> ${store.category || 'N/A'}
<strong>Notable Products:</strong> ${(store.notable_products || []).join(', ') || 'N/A'}

<strong>Short Description:</strong>
${store.description || 'N/A'}

<strong>Extended Description:</strong>
${store.extended_description || 'N/A'}

<strong>Processing Metadata:</strong>
Processed at: ${store.processing_metadata?.processed_at || 'N/A'}
Sitemaps found: ${store.processing_metadata?.sitemaps_found || 0}
URLs analyzed: ${store.processing_metadata?.urls_analyzed || 0}
            `;
            
            // URL Analysis tab
            document.getElementById('url-analysis').textContent = 
                store.sitemap_analysis || 'No URL analysis available';
            
            // Full Description tab
            const description = store.detailed_description || 'No detailed description available';
            if (description !== 'No detailed description available') {
                // Format the description with proper HTML
                const formattedDesc = description
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/\n/g, '<br>')
                    .replace(/^/, '<p>')
                    .replace(/$/, '</p>');
                document.getElementById('full-description').innerHTML = formattedDesc;
            } else {
                document.getElementById('full-description').textContent = description;
            }
            
            // Raw Data tab
            document.getElementById('raw-data').textContent = 
                JSON.stringify(store, null, 2);
            
            modal.style.display = 'block';
            switchTab('overview');
        }
        
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName).classList.add('active');
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }
        
        // Try to load default file on page load
        window.onload = function() {
            // You can uncomment this to auto-load a specific file
            // fetch('output_5_stores.jsonl').then(r => r.text()).then(data => {
            //     const lines = data.split('\n').filter(line => line.trim());
            //     stores = lines.map(line => JSON.parse(line));
            //     displayStores();
            // });
        }
    </script>
</body>
</html>