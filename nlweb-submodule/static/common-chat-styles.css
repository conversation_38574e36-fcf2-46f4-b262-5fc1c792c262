/* Common Chat Styles - Shared across all chat interfaces */

:root {
  --sidebar-width: 260px;
  --primary-color: #5e5eff;
  --text-primary: #0d0d0d;
  --text-secondary: #666;
  --bg-primary: #ffffff;
  --bg-secondary: #f7f7f8;
  --bg-sidebar: #f9f9f9;
  --border-color: #e5e5e5;
  --hover-bg: #f0f0f0;
  --message-user-bg: #f7f7f8;
  --message-assistant-bg: #ffffff;
  --input-bg: #ffffff;
  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
  --transition: all 0.2s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #d0d0d0;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b0b0b0;
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-sidebar);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  position: relative;
  height: 100vh;
}

.sidebar.collapsed {
  width: 0;
  overflow: hidden;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  position: fixed;
  left: var(--sidebar-width);
  top: 20px;
  width: 32px;
  height: 32px;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0 6px 6px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: left var(--transition), border-radius var(--transition);
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.sidebar-toggle.sidebar-collapsed {
  left: 0;
  border-radius: 0 6px 6px 0;
  box-shadow: 2px 0 6px rgba(0,0,0,0.1);
}

.sidebar-toggle:hover {
  background-color: var(--hover-bg);
  box-shadow: var(--shadow-md);
}

.sidebar-toggle svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.sidebar-toggle.sidebar-collapsed svg {
  transform: rotate(180deg);
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

/* Conversations List Components */
.conversations-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
}

.conversations-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.new-chat-icon-btn {
  width: 26px;
  height: 26px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  flex-shrink: 0;
}

.new-chat-icon-btn:hover {
  background-color: var(--hover-bg);
  border-color: var(--primary-color);
}

.plus-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  color: var(--text-secondary);
}

.new-chat-icon-btn:hover .plus-icon {
  color: var(--primary-color);
}

.new-chat-icon-btn svg {
  width: 14px;
  height: 14px;
  color: var(--text-secondary);
}

.new-chat-icon-btn:hover svg {
  color: var(--primary-color);
}

.header-buttons {
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: center;
}

.conversations-list {
  max-height: 60%;
  overflow-y: auto;
  padding: 8px;
  flex-shrink: 0;
}

/* When no remembered section exists, conversations should use all space */
.sidebar:not(.has-remembered) .conversations-list {
  max-height: calc(100% - 60px); /* Account for header */
  flex: 1;
}

/* Sidebar footer */
.sidebar-footer {
  padding: 12px;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.storage-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 6px;
  transition: var(--transition);
  font-size: 14px;
}

.storage-link:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.storage-link svg {
  width: 16px;
  height: 16px;
}

/* Conversation Items */
.conversation-item {
  padding: 1px 0;
  margin-bottom: 1px;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0;
}

.conversation-item:hover {
  background-color: var(--hover-bg);
}

.conversation-item.active {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

.conversation-content {
  flex: 1;
  min-width: 0;
  padding-right: 12px;
}

.conversation-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-delete {
  opacity: 0;
  width: 12px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: var(--transition);
  flex-shrink: 0;
  font-size: 20px;
  line-height: 1;
  font-weight: 300;
  margin-left: 8px;
  margin-right: 2px;
}

.conversation-item:hover .conversation-delete {
  opacity: 0.7;
}

.conversation-delete:hover {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff4444;
  opacity: 1;
}

/* Site Groups */
.site-group {
  margin-bottom: 20px;
}

.site-group:last-child {
  margin-bottom: 0;
}

/* Site Group Headers */
.site-group-header {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 600;
  padding: 6px 12px 2px 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition);
  user-select: none;
}

.site-group-header:hover {
  color: var(--text-primary);
}

.site-group-header .chevron {
  width: 12px;
  height: 12px;
  transition: transform 0.2s ease;
}

.site-group-header.collapsed .chevron {
  transform: rotate(-90deg);
}

.site-conversations {
  overflow: visible;
  max-height: 1000px;
  transition: max-height 0.3s ease;
}

.site-conversations.collapsed {
  max-height: 0;
}

.site-conversations .conversation-item {
  padding-left: 24px;
  margin-bottom: 0;
}

.site-conversations .conversation-item:not(:last-child) {
  margin-bottom: 1px;
}

/* Remembered Items Section */
#remembered-section {
  flex: 1;
  overflow-y: auto;
  border-top: 1px solid var(--border-color);
  padding: 16px;
  min-height: 0;
}

/* Main Chat Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  height: 60px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 24px 0 56px;
  background-color: var(--bg-primary);
  gap: 16px;
  transition: padding-left var(--transition);
}

.sidebar.collapsed ~ .main-content .chat-header {
  padding-left: 56px;
}

.chat-title {
  font-size: 16px;
  font-weight: 600;
}

.chat-site-info {
  font-size: 14px;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 500;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: var(--bg-primary);
}

.messages-container {
  width: 100%;
}

.message {
  margin-bottom: 24px;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.user-message .message-avatar {
  background-color: var(--primary-color);
  color: white;
}

.user-message .message-text {
  font-weight: bold;
}

.assistant-message .message-avatar {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.message-content {
  flex: 1;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Debug Info */
.message-debug-icon {
  margin-left: 12px;
  padding: 4px 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: var(--transition);
  display: inline-block;
}

.message-debug-icon:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Debug button in header */
.debug-button {
  padding: 2px 6px;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 8px;
  color: var(--text-secondary);
}

.debug-button:hover {
  background-color: var(--hover-bg);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.debug-info {
  background-color: #f0f0f0;
  padding: 16px;
  margin: 12px 0;
  border-radius: 8px;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Loading Animation */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dot {
  width: 8px;
  height: 8px;
  background-color: var(--text-secondary);
  border-radius: 50%;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Streaming Spinner - Initial (before sites list) */
.streaming-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #e5e5e5;
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spinner-rotate 0.8s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
}

/* Secondary Spinner - After sites list, waiting for results */
.streaming-spinner-secondary {
  display: inline-flex;
  gap: 4px;
  align-items: center;
  margin: 12px 0;
}

.streaming-spinner-secondary span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
  animation: pulse-fade 1.4s ease-in-out infinite;
}

.streaming-spinner-secondary span:nth-child(1) {
  animation-delay: 0s;
}

.streaming-spinner-secondary span:nth-child(2) {
  animation-delay: 0.2s;
}

.streaming-spinner-secondary span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes spinner-rotate {
  to { 
    transform: rotate(360deg); 
  }
}

@keyframes pulse-fade {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.streaming-message.with-spinner .message-text {
  display: flex;
  align-items: center;
  min-height: 24px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: 16px;
  left: 16px;
  z-index: 1000;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

/* Item Container Styles (for search results) */
.item-container {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  gap: 16px;
  width: 100%;
}

.item-content {
  flex: 1;
}

.item-title-row {
  margin-bottom: 8px;
}

.item-title-link {
  color: #0066cc;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3;
}

.item-title-link:hover {
  text-decoration: underline;
}

.item-site-link {
  display: inline-block;
  margin-top: 4px;
  margin-bottom: 8px;
  color: #6b7280;
  text-decoration: none;
  font-size: 13px;
  padding: 2px 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
}

.item-site-link:hover {
  text-decoration: underline;
  background-color: #e5e7eb;
}

.item-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin: 8px 0;
}

.item-details-text {
  display: inline-block;
  margin: 4px 0;
  padding: 4px 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  color: #495057;
}

.item-explanation {
  font-style: italic;
  color: #666;
  margin-top: 8px;
  font-size: 14px;
}

.item-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -var(--sidebar-width);
    z-index: 999;
    height: 100vh;
    box-shadow: var(--shadow-md);
  }

  .sidebar.open {
    left: 0;
  }
  
  .sidebar.collapsed {
    left: -var(--sidebar-width);
  }
  
  .sidebar-toggle {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .chat-header {
    padding-left: 60px;
  }
}