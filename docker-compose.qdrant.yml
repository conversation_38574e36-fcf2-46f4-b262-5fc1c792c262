services:
  # Add a database service if needed
  qdrant:
    image: qdrant/qdrant:latest
    container_name: nlweb-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__API_KEY: "<OPTIONAL>"       
    networks:
      - qdrant-net

networks:
  qdrant-net:
    external: true     # don’t recreate, reuse the existing network

volumes:
   qdrant_data:
