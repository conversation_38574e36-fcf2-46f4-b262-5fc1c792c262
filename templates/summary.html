<!DOCTYPE html>
<html>
<head>
    <title>Crawler Summary</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .navigation {
            margin-bottom: 20px;
        }
        .navigation a {
            margin-right: 15px;
            text-decoration: none;
            color: #4CAF50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            color: #4CAF50;
        }
        .table-container {
            margin: 30px 0;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
        .status-running {
            color: green;
        }
        .status-paused {
            color: orange;
        }
        .site-link {
            color: #2196F3;
            text-decoration: none;
            font-weight: bold;
        }
        .site-link:hover {
            text-decoration: underline;
            color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="/">Summary</a>
        <a href="/status">Status</a>
        <a href="/add">Add Site</a>
        <a href="/log">Log</a>
        <a href="/error_log">Error Log</a>
    </div>
    
    <h1>Crawler Summary</h1>
    
    <div class="summary-stats">
        <div class="stat-card">
            <h3>Total Sites</h3>
            <div class="stat-value" id="totalSites">0</div>
        </div>
        <div class="stat-card">
            <h3>Total URLs</h3>
            <div class="stat-value" id="totalUrls">0</div>
        </div>
        <div class="stat-card">
            <h3>Crawled URLs</h3>
            <div class="stat-value" id="crawledUrls">0</div>
        </div>
        <div class="stat-card">
            <h3>Overall Progress</h3>
            <div class="stat-value" id="overallProgress">0%</div>
        </div>
    </div>
    
    <div class="table-container">
        <h2>Sites Overview</h2>
        <table id="sitesTable">
            <thead>
                <tr>
                    <th>Site</th>
                    <th>Status</th>
                    <th>Total URLs</th>
                    <th>Crawled</th>
                    <th>JSON Objects</th>
                    <th>Progress</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="5" style="text-align: center;">Loading...</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <script>
        async function loadSummary() {
            try {
                const response = await fetch('/sites');
                const sites = await response.json();
                
                // Calculate summary statistics
                let totalSites = sites.length;
                let totalUrls = 0;
                let crawledUrls = 0;
                let runningCount = 0;
                let pausedCount = 0;
                let totalJsonObjects = 0;
                
                sites.forEach(site => {
                    totalUrls += site.total_urls;
                    crawledUrls += site.crawled_urls;
                    totalJsonObjects += (site.json_objects || 0);
                    if (site.paused) {
                        pausedCount++;
                    } else {
                        runningCount++;
                    }
                });
                
                const overallProgress = totalUrls > 0 
                    ? Math.round((crawledUrls / totalUrls) * 100) 
                    : 0;
                
                // Update summary stats
                document.getElementById('totalSites').textContent = totalSites;
                document.getElementById('totalUrls').textContent = totalUrls.toLocaleString();
                document.getElementById('crawledUrls').textContent = crawledUrls.toLocaleString();
                document.getElementById('overallProgress').textContent = overallProgress + '%';
                
                // Update sites table
                const tbody = document.querySelector('#sitesTable tbody');
                
                if (sites.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">No sites being crawled yet.</td></tr>';
                    return;
                }
                
                let html = '';
                sites.forEach(site => {
                    const progress = site.total_urls > 0 
                        ? Math.round((site.crawled_urls / site.total_urls) * 100) 
                        : 0;
                    
                    html += `
                        <tr>
                            <td><a href="/status/${site.name}" class="site-link">${site.name}</a></td>
                            <td class="status-${site.paused ? 'paused' : 'running'}">
                                ${site.paused ? 'Paused' : 'Running'}
                            </td>
                            <td>${site.total_urls.toLocaleString()}</td>
                            <td>${site.crawled_urls.toLocaleString()}</td>
                            <td>${(site.json_objects || 0).toLocaleString()}</td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progress}%"></div>
                                </div>
                                <small>${progress}%</small>
                            </td>
                        </tr>
                    `;
                });
                
                tbody.innerHTML = html;
                
            } catch (error) {
                console.error('Error loading summary:', error);
                document.querySelector('#sitesTable tbody').innerHTML = 
                    `<tr><td colspan="5" style="color: red; text-align: center;">Error loading summary: ${error.message}</td></tr>`;
            }
        }
        
        // Load summary on page load
        loadSummary();
        
        // Refresh every 5 seconds
        setInterval(loadSummary, 5000);
    </script>
</body>
</html>